{"/_not-found/page": "app/_not-found/page.js", "/about/page": "app/about/page.js", "/admin/analytics/page": "app/admin/analytics/page.js", "/admin/applications/page": "app/admin/applications/page.js", "/admin/page": "app/admin/page.js", "/admin/posts/page": "app/admin/posts/page.js", "/admin/projects/new/page": "app/admin/projects/new/page.js", "/admin/schedule/auto/page": "app/admin/schedule/auto/page.js", "/admin/schedule/page": "app/admin/schedule/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/system/page": "app/admin/system/page.js", "/admin/team-members/new/page": "app/admin/team-members/new/page.js", "/admin/team-members/page": "app/admin/team-members/page.js", "/admin/users/page": "app/admin/users/page.js", "/api/admin/applications/[id]/route": "app/api/admin/applications/[id]/route.js", "/api/admin/dashboard/route": "app/api/admin/dashboard/route.js", "/api/admin/schedules/[id]/route": "app/api/admin/schedules/[id]/route.js", "/api/admin/schedules/route": "app/api/admin/schedules/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/applications/route": "app/api/applications/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/posts/route": "app/api/posts/route.js", "/api/posts/slug/[slug]/route": "app/api/posts/slug/[slug]/route.js", "/api/projects/[id]/route": "app/api/projects/[id]/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/settings/public/route": "app/api/settings/public/route.js", "/api/team-members/route": "app/api/team-members/route.js", "/apply/page": "app/apply/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/blog/[slug]/page": "app/blog/[slug]/page.js", "/blog/page": "app/blog/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js", "/projects/[id]/page": "app/projects/[id]/page.js", "/projects/page": "app/projects/page.js", "/team/page": "app/team/page.js"}
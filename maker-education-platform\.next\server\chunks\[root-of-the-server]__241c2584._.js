module.exports = {

"[project]/.next-internal/server/app/api/settings/public/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ensureDbConnection": (()=>ensureDbConnection),
    "prisma": (()=>prisma),
    "safeDbOperation": (()=>safeDbOperation)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
// 创建带有重试机制的Prisma客户端
const createPrismaClient = ()=>{
    return new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
        log: ("TURBOPACK compile-time truthy", 1) ? [
            'error',
            'warn'
        ] : ("TURBOPACK unreachable", undefined)
    });
};
const prisma = globalForPrisma.prisma ?? createPrismaClient();
if ("TURBOPACK compile-time truthy", 1) {
    globalForPrisma.prisma = prisma;
}
async function ensureDbConnection() {
    try {
        await prisma.$connect();
        return true;
    } catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
}
async function safeDbOperation(operation, fallback) {
    try {
        await ensureDbConnection();
        return await operation();
    } catch (error) {
        console.error('Database operation failed:', error);
        return fallback;
    }
}
}}),
"[project]/src/app/api/settings/public/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "dynamic": (()=>dynamic),
    "revalidate": (()=>revalidate)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
const dynamic = 'force-dynamic';
const revalidate = 0;
async function GET() {
    const defaultSettings = {
        // 基本信息
        site_name: '创客教育平台',
        site_description: '专业的STEAM教育平台',
        site_slogan: '创新思维 · 实践能力',
        team_count: 50,
        copyright_year: '2024',
        // 联系信息
        contact_phone: '************',
        contact_email: '<EMAIL>',
        contact_address: '北京市海淀区创新大厦',
        contact_wechat: 'makeredu2024',
        contact_qq: '123456789',
        work_hours: '周一至周五 9:00-18:00',
        // 功能开关
        allow_registration: true,
        show_team_section: true,
        show_projects_section: true,
        show_blog_section: true,
        show_apply_section: true,
        enable_comments: true,
        enable_likes: true,
        maintenance_mode: false,
        require_approval: true,
        // 外观设置
        primary_color: '#3B82F6',
        secondary_color: '#6366F1',
        hero_background: 'gradient',
        show_animations: true,
        dark_mode_enabled: false,
        // 首页内容
        hero_title: '创新思维 · 实践能力',
        hero_subtitle: 'STEAM创客教育平台',
        hero_description: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',
        about_intro: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',
        // 统计数据
        stats_projects: 500,
        stats_students: 1000,
        stats_satisfaction: 98,
        total_members: 50,
        active_members: 35,
        total_projects: 120,
        completed_projects: 95,
        // SEO设置
        meta_keywords: 'STEAM教育,创客教育,3D打印,机器人,编程',
        meta_description: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程',
        // 法律文档
        terms_content: '',
        privacy_content: '',
        terms_last_updated: new Date().toISOString().split('T')[0],
        privacy_last_updated: new Date().toISOString().split('T')[0],
        // 自定义数据
        facilities_equipment: '[]',
        classroom_specs: '[]',
        main_equipment: '[]'
    };
    const publicSettingsKeys = [
        // 基本信息
        'site_name',
        'site_description',
        'site_slogan',
        'team_count',
        'copyright_year',
        // 联系信息
        'contact_phone',
        'contact_email',
        'contact_address',
        'contact_wechat',
        'contact_qq',
        'work_hours',
        // 功能开关
        'allow_registration',
        'show_team_section',
        'show_projects_section',
        'show_blog_section',
        'show_apply_section',
        'enable_comments',
        'enable_likes',
        'maintenance_mode',
        'require_approval',
        // 外观设置
        'primary_color',
        'secondary_color',
        'hero_background',
        'show_animations',
        'dark_mode_enabled',
        // 首页内容
        'hero_title',
        'hero_subtitle',
        'hero_description',
        'about_intro',
        // 统计数据
        'stats_projects',
        'stats_students',
        'stats_satisfaction',
        'total_members',
        'active_members',
        'total_projects',
        'completed_projects',
        // SEO设置
        'meta_keywords',
        'meta_description',
        // 法律文档
        'terms_content',
        'privacy_content',
        'terms_last_updated',
        'privacy_last_updated',
        // 自定义数据
        'facilities_equipment',
        'classroom_specs',
        'main_equipment'
    ];
    const dbSettings = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safeDbOperation"])(async ()=>{
        const { prisma } = await __turbopack_context__.r("[project]/src/lib/prisma.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        return await prisma.setting.findMany({
            where: {
                key: {
                    in: publicSettingsKeys
                }
            }
        });
    }, []);
    // 转换为键值对对象
    const settings = {
        ...defaultSettings
    };
    dbSettings.forEach((setting)=>{
        let value = setting.value;
        // 类型转换
        if (setting.type === 'BOOLEAN') {
            value = value === 'true';
        } else if (setting.type === 'NUMBER') {
            value = parseInt(value) || 0;
        }
        settings[setting.key] = value;
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(settings, {
        headers: {
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Surrogate-Control': 'no-store'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__241c2584._.js.map
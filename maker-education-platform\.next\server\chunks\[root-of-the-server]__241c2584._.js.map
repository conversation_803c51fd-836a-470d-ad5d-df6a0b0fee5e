{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\n// 创建带有重试机制的Prisma客户端\nconst createPrismaClient = () => {\n  return new PrismaClient({\n    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],\n  })\n}\n\nexport const prisma = globalForPrisma.prisma ?? createPrismaClient()\n\nif (process.env.NODE_ENV !== 'production') {\n  globalForPrisma.prisma = prisma\n}\n\n// 确保数据库连接\nexport async function ensureDbConnection() {\n  try {\n    await prisma.$connect()\n    return true\n  } catch (error) {\n    console.error('Database connection failed:', error)\n    return false\n  }\n}\n\n// 安全的数据库操作包装器\nexport async function safeDbOperation<T>(operation: () => Promise<T>, fallback: T): Promise<T> {\n  try {\n    await ensureDbConnection()\n    return await operation()\n  } catch (error) {\n    console.error('Database operation failed:', error)\n    return fallback\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIxB,qBAAqB;AACrB,MAAM,qBAAqB;IACzB,OAAO,IAAI,6HAAA,CAAA,eAAY,CAAC;QACtB,KAAK,uCAAyC;YAAC;YAAS;SAAO;IACjE;AACF;AAEO,MAAM,SAAS,gBAAgB,MAAM,IAAI;AAEhD,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;AAC3B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,QAAQ;QACrB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,eAAe,gBAAmB,SAA2B,EAAE,QAAW;IAC/E,IAAI;QACF,MAAM;QACN,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/settings/public/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { safeDbOperation } from '@/lib/prisma'\n\n// 启用缓存以提高性能\nexport const dynamic = 'force-dynamic'\nexport const revalidate = 60 // 缓存60秒\n\n// 内存缓存\nlet cachedSettings: any = null\nlet cacheTimestamp = 0\nconst CACHE_DURATION = 60000 // 60秒缓存\n\nexport async function GET() {\n  // 检查内存缓存\n  const now = Date.now()\n  if (cachedSettings && (now - cacheTimestamp) < CACHE_DURATION) {\n    return NextResponse.json(cachedSettings, {\n      headers: {\n        'Cache-Control': 'public, max-age=60, s-maxage=60',\n        'Vary': 'Accept-Encoding',\n        'X-Cache': 'HIT'\n      }\n    })\n  }\n\n  const defaultSettings = {\n    // 基本信息\n    site_name: '创客教育平台',\n    site_description: '专业的STEAM教育平台',\n    site_slogan: '创新思维 · 实践能力',\n    team_count: 50,\n    copyright_year: '2024',\n\n    // 联系信息\n    contact_phone: '************',\n    contact_email: '<EMAIL>',\n    contact_address: '北京市海淀区创新大厦',\n    contact_wechat: 'makeredu2024',\n    contact_qq: '123456789',\n    work_hours: '周一至周五 9:00-18:00',\n\n    // 功能开关\n    allow_registration: true,\n    show_team_section: true,\n    show_projects_section: true,\n    show_blog_section: true,\n    show_apply_section: true,\n    enable_comments: true,\n    enable_likes: true,\n    maintenance_mode: false,\n    require_approval: true,\n\n    // 外观设置\n    primary_color: '#3B82F6',\n    secondary_color: '#6366F1',\n    hero_background: 'gradient',\n    show_animations: true,\n    dark_mode_enabled: false,\n\n    // 首页内容\n    hero_title: '创新思维 · 实践能力',\n    hero_subtitle: 'STEAM创客教育平台',\n    hero_description: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',\n    about_intro: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',\n    cta_title: '准备好开始你的创客之旅了吗？',\n    cta_description: '加入我们的创客教育平台，与志同道合的伙伴一起探索科技的无限可能',\n\n    // 统计数据\n    stats_projects: 500,\n    stats_students: 1000,\n    stats_satisfaction: 98,\n    total_members: 50,\n    active_members: 35,\n    total_projects: 120,\n    completed_projects: 95,\n\n    // SEO设置\n    meta_keywords: 'STEAM教育,创客教育,3D打印,机器人,编程',\n    meta_description: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程',\n    \n    // 法律文档\n    terms_content: '',\n    privacy_content: '',\n    terms_last_updated: new Date().toISOString().split('T')[0],\n    privacy_last_updated: new Date().toISOString().split('T')[0],\n    \n    // 自定义数据\n    facilities_equipment: '[]',\n    classroom_specs: '[]',\n    main_equipment: '[]'\n  }\n\n  const publicSettingsKeys = [\n    // 基本信息\n    'site_name', 'site_description', 'site_slogan', 'team_count', 'copyright_year',\n    // 联系信息\n    'contact_phone', 'contact_email', 'contact_address', 'contact_wechat', 'contact_qq', 'work_hours',\n    // 功能开关\n    'allow_registration', 'show_team_section', 'show_projects_section', 'show_blog_section',\n    'show_apply_section', 'enable_comments', 'enable_likes', 'maintenance_mode', 'require_approval',\n    // 外观设置\n    'primary_color', 'secondary_color', 'hero_background', 'show_animations', 'dark_mode_enabled',\n    // 首页内容\n    'hero_title', 'hero_subtitle', 'hero_description', 'about_intro', 'cta_title', 'cta_description',\n    // 统计数据\n    'stats_projects', 'stats_students', 'stats_satisfaction', 'total_members', 'active_members',\n    'total_projects', 'completed_projects',\n    // SEO设置\n    'meta_keywords', 'meta_description',\n    // 法律文档\n    'terms_content', 'privacy_content', 'terms_last_updated', 'privacy_last_updated',\n    // 自定义数据\n    'facilities_equipment', 'classroom_specs', 'main_equipment'\n  ]\n\n  const dbSettings = await safeDbOperation(async () => {\n    const { prisma } = await import('@/lib/prisma')\n    return await prisma.setting.findMany({\n      where: {\n        key: {\n          in: publicSettingsKeys\n        }\n      },\n      select: {\n        key: true,\n        value: true,\n        type: true\n      }\n    })\n  }, [])\n\n  // 转换为键值对对象\n  const settings: Record<string, any> = { ...defaultSettings }\n\n  dbSettings.forEach((setting: any) => {\n    let value = setting.value\n\n    // 类型转换\n    if (setting.type === 'BOOLEAN') {\n      value = value === 'true'\n    } else if (setting.type === 'NUMBER') {\n      value = parseInt(value) || 0\n    }\n\n    settings[setting.key] = value\n  })\n\n  // 更新缓存\n  cachedSettings = settings\n  cacheTimestamp = now\n\n  return NextResponse.json(settings, {\n    headers: {\n      'Cache-Control': 'public, max-age=60, s-maxage=60',\n      'Vary': 'Accept-Encoding',\n      'X-Cache': 'MISS'\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,MAAM,UAAU;AAChB,MAAM,aAAa,GAAG,QAAQ;;AAErC,OAAO;AACP,IAAI,iBAAsB;AAC1B,IAAI,iBAAiB;AACrB,MAAM,iBAAiB,MAAM,QAAQ;;AAE9B,eAAe;IACpB,SAAS;IACT,MAAM,MAAM,KAAK,GAAG;IACpB,IAAI,kBAAkB,AAAC,MAAM,iBAAkB,gBAAgB;QAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,gBAAgB;YACvC,SAAS;gBACP,iBAAiB;gBACjB,QAAQ;gBACR,WAAW;YACb;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAO;QACP,WAAW;QACX,kBAAkB;QAClB,aAAa;QACb,YAAY;QACZ,gBAAgB;QAEhB,OAAO;QACP,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,gBAAgB;QAChB,YAAY;QACZ,YAAY;QAEZ,OAAO;QACP,oBAAoB;QACpB,mBAAmB;QACnB,uBAAuB;QACvB,mBAAmB;QACnB,oBAAoB;QACpB,iBAAiB;QACjB,cAAc;QACd,kBAAkB;QAClB,kBAAkB;QAElB,OAAO;QACP,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;QAEnB,OAAO;QACP,YAAY;QACZ,eAAe;QACf,kBAAkB;QAClB,aAAa;QACb,WAAW;QACX,iBAAiB;QAEjB,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,oBAAoB;QACpB,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,oBAAoB;QAEpB,QAAQ;QACR,eAAe;QACf,kBAAkB;QAElB,OAAO;QACP,eAAe;QACf,iBAAiB;QACjB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1D,sBAAsB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAE5D,QAAQ;QACR,sBAAsB;QACtB,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB;QACzB,OAAO;QACP;QAAa;QAAoB;QAAe;QAAc;QAC9D,OAAO;QACP;QAAiB;QAAiB;QAAmB;QAAkB;QAAc;QACrF,OAAO;QACP;QAAsB;QAAqB;QAAyB;QACpE;QAAsB;QAAmB;QAAgB;QAAoB;QAC7E,OAAO;QACP;QAAiB;QAAmB;QAAmB;QAAmB;QAC1E,OAAO;QACP;QAAc;QAAiB;QAAoB;QAAe;QAAa;QAC/E,OAAO;QACP;QAAkB;QAAkB;QAAsB;QAAiB;QAC3E;QAAkB;QAClB,QAAQ;QACR;QAAiB;QACjB,OAAO;QACP;QAAiB;QAAmB;QAAsB;QAC1D,QAAQ;QACR;QAAwB;QAAmB;KAC5C;IAED,MAAM,aAAa,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACvC,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,OAAO,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;YACnC,OAAO;gBACL,KAAK;oBACH,IAAI;gBACN;YACF;YACA,QAAQ;gBACN,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;QACF;IACF,GAAG,EAAE;IAEL,WAAW;IACX,MAAM,WAAgC;QAAE,GAAG,eAAe;IAAC;IAE3D,WAAW,OAAO,CAAC,CAAC;QAClB,IAAI,QAAQ,QAAQ,KAAK;QAEzB,OAAO;QACP,IAAI,QAAQ,IAAI,KAAK,WAAW;YAC9B,QAAQ,UAAU;QACpB,OAAO,IAAI,QAAQ,IAAI,KAAK,UAAU;YACpC,QAAQ,SAAS,UAAU;QAC7B;QAEA,QAAQ,CAAC,QAAQ,GAAG,CAAC,GAAG;IAC1B;IAEA,OAAO;IACP,iBAAiB;IACjB,iBAAiB;IAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;QACjC,SAAS;YACP,iBAAiB;YACjB,QAAQ;YACR,WAAW;QACb;IACF;AACF", "debugId": null}}]}
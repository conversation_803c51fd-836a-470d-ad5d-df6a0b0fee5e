{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/settings/public/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\n// 禁用缓存，确保每次都获取最新数据\nexport const dynamic = 'force-dynamic'\nexport const revalidate = 0\n\nexport async function GET() {\n  try {\n    const publicSettings = await prisma.setting.findMany({\n      where: {\n        key: {\n          in: [\n            // 基本信息\n            'site_name', 'site_description', 'site_slogan', 'team_count', 'copyright_year',\n            // 联系信息\n            'contact_phone', 'contact_email', 'contact_address', 'contact_wechat', 'contact_qq', 'work_hours',\n            // 功能开关\n            'allow_registration', 'show_team_section', 'show_projects_section', 'show_blog_section',\n            'show_apply_section', 'enable_comments', 'enable_likes', 'maintenance_mode',\n            // 外观设置\n            'primary_color', 'secondary_color', 'hero_background', 'show_animations', 'dark_mode_enabled',\n            // 首页内容\n            'hero_title', 'hero_subtitle', 'hero_description', 'about_intro',\n            // 统计数据\n            'stats_projects', 'stats_students', 'stats_satisfaction', 'total_members', 'active_members',\n            'total_projects', 'completed_projects',\n            // SEO设置\n            'meta_keywords', 'meta_description',\n            // 法律文档\n            'terms_content', 'privacy_content', 'terms_last_updated', 'privacy_last_updated',\n            // 自定义数据\n            'facilities_equipment', 'classroom_specs', 'main_equipment'\n          ]\n        }\n      }\n    })\n\n    const settingsMap = publicSettings.reduce((acc, setting) => {\n      let value: any = setting.value\n      if (setting.type === 'BOOLEAN') {\n        value = setting.value === 'true'\n      } else if (setting.type === 'NUMBER') {\n        value = parseFloat(setting.value)\n      }\n      acc[setting.key] = value\n      return acc\n    }, {} as any)\n\n    // 设置默认值\n    const defaults = {\n      // 基本信息\n      site_name: '创客教育平台',\n      site_description: '专业的STEAM教育平台',\n      site_slogan: '创新思维 · 实践能力',\n      team_count: 50,\n      copyright_year: '2024',\n\n      // 联系信息\n      contact_phone: '************',\n      contact_email: '<EMAIL>',\n      contact_address: '北京市海淀区创新大厦',\n      contact_wechat: 'makeredu2024',\n      contact_qq: '123456789',\n      work_hours: '周一至周五 9:00-18:00',\n\n      // 功能开关\n      allow_registration: true,\n      show_team_section: true,\n      show_projects_section: true,\n      show_blog_section: true,\n      show_apply_section: true,\n      enable_comments: true,\n      enable_likes: true,\n      maintenance_mode: false,\n\n      // 外观设置\n      primary_color: '#3B82F6',\n      secondary_color: '#6366F1',\n      hero_background: 'gradient',\n      show_animations: true,\n      dark_mode_enabled: false,\n\n      // 首页内容\n      hero_title: '创新思维 · 实践能力',\n      hero_subtitle: 'STEAM创客教育平台',\n      hero_description: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',\n      about_intro: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',\n\n      // 统计数据\n      stats_projects: 500,\n      stats_students: 1000,\n      stats_satisfaction: 98,\n      total_members: 50,\n      active_members: 35,\n      total_projects: 120,\n      completed_projects: 95,\n\n      // SEO设置\n      meta_keywords: 'STEAM教育,创客教育,3D打印,机器人,编程',\n      meta_description: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程',\n\n      // 法律文档\n      terms_content: '',\n      privacy_content: '',\n      terms_last_updated: new Date().toISOString().split('T')[0],\n      privacy_last_updated: new Date().toISOString().split('T')[0],\n\n      // 自定义数据\n      facilities_equipment: '[]',\n      classroom_specs: '[]',\n      main_equipment: '[]'\n    }\n\n    return NextResponse.json({ ...defaults, ...settingsMap })\n  } catch (error) {\n    console.error('Failed to fetch public settings:', error)\n    // 返回默认设置\n    return NextResponse.json({\n      site_name: '创客教育平台',\n      site_description: '专业的STEAM教育平台',\n      site_slogan: '创新思维 · 实践能力',\n      team_count: 50,\n      copyright_year: '2024',\n      contact_phone: '************',\n      contact_email: '<EMAIL>',\n      contact_address: '北京市海淀区创新大厦',\n      contact_wechat: 'makeredu2024',\n      contact_qq: '123456789',\n      work_hours: '周一至周五 9:00-18:00',\n      allow_registration: true,\n      show_team_section: true,\n      show_projects_section: true,\n      show_blog_section: true,\n      show_apply_section: true,\n      enable_comments: true,\n      enable_likes: true,\n      maintenance_mode: false,\n      primary_color: '#3B82F6',\n      secondary_color: '#6366F1',\n      show_animations: true,\n      hero_title: '创新思维 · 实践能力',\n      hero_subtitle: 'STEAM创客教育平台',\n      stats_projects: 500,\n      stats_students: 1000,\n      stats_satisfaction: 98,\n      total_members: 50,\n      active_members: 35,\n      total_projects: 120,\n      completed_projects: 95,\n      terms_content: '',\n      privacy_content: '',\n      terms_last_updated: new Date().toISOString().split('T')[0],\n      privacy_last_updated: new Date().toISOString().split('T')[0],\n      facilities_equipment: '[]',\n      classroom_specs: '[]',\n      main_equipment: '[]'\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,MAAM,UAAU;AAChB,MAAM,aAAa;AAEnB,eAAe;IACpB,IAAI;QACF,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACnD,OAAO;gBACL,KAAK;oBACH,IAAI;wBACF,OAAO;wBACP;wBAAa;wBAAoB;wBAAe;wBAAc;wBAC9D,OAAO;wBACP;wBAAiB;wBAAiB;wBAAmB;wBAAkB;wBAAc;wBACrF,OAAO;wBACP;wBAAsB;wBAAqB;wBAAyB;wBACpE;wBAAsB;wBAAmB;wBAAgB;wBACzD,OAAO;wBACP;wBAAiB;wBAAmB;wBAAmB;wBAAmB;wBAC1E,OAAO;wBACP;wBAAc;wBAAiB;wBAAoB;wBACnD,OAAO;wBACP;wBAAkB;wBAAkB;wBAAsB;wBAAiB;wBAC3E;wBAAkB;wBAClB,QAAQ;wBACR;wBAAiB;wBACjB,OAAO;wBACP;wBAAiB;wBAAmB;wBAAsB;wBAC1D,QAAQ;wBACR;wBAAwB;wBAAmB;qBAC5C;gBACH;YACF;QACF;QAEA,MAAM,cAAc,eAAe,MAAM,CAAC,CAAC,KAAK;YAC9C,IAAI,QAAa,QAAQ,KAAK;YAC9B,IAAI,QAAQ,IAAI,KAAK,WAAW;gBAC9B,QAAQ,QAAQ,KAAK,KAAK;YAC5B,OAAO,IAAI,QAAQ,IAAI,KAAK,UAAU;gBACpC,QAAQ,WAAW,QAAQ,KAAK;YAClC;YACA,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG;YACnB,OAAO;QACT,GAAG,CAAC;QAEJ,QAAQ;QACR,MAAM,WAAW;YACf,OAAO;YACP,WAAW;YACX,kBAAkB;YAClB,aAAa;YACb,YAAY;YACZ,gBAAgB;YAEhB,OAAO;YACP,eAAe;YACf,eAAe;YACf,iBAAiB;YACjB,gBAAgB;YAChB,YAAY;YACZ,YAAY;YAEZ,OAAO;YACP,oBAAoB;YACpB,mBAAmB;YACnB,uBAAuB;YACvB,mBAAmB;YACnB,oBAAoB;YACpB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAElB,OAAO;YACP,eAAe;YACf,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB,mBAAmB;YAEnB,OAAO;YACP,YAAY;YACZ,eAAe;YACf,kBAAkB;YAClB,aAAa;YAEb,OAAO;YACP,gBAAgB;YAChB,gBAAgB;YAChB,oBAAoB;YACpB,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,oBAAoB;YAEpB,QAAQ;YACR,eAAe;YACf,kBAAkB;YAElB,OAAO;YACP,eAAe;YACf,iBAAiB;YACjB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,sBAAsB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAE5D,QAAQ;YACR,sBAAsB;YACtB,iBAAiB;YACjB,gBAAgB;QAClB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,GAAG,QAAQ;YAAE,GAAG,WAAW;QAAC;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,SAAS;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,WAAW;YACX,kBAAkB;YAClB,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,iBAAiB;YACjB,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ,oBAAoB;YACpB,mBAAmB;YACnB,uBAAuB;YACvB,mBAAmB;YACnB,oBAAoB;YACpB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,eAAe;YACf,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,oBAAoB;YACpB,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,oBAAoB;YACpB,eAAe;YACf,iBAAiB;YACjB,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,sBAAsB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5D,sBAAsB;YACtB,iBAAiB;YACjB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}]}
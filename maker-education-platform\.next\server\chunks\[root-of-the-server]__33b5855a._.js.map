{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar ?? undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session, context)\n    } catch (error) {\n      console.error('Admin auth error:', error)\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/admin/schedules/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { withAdmin } from '@/lib/auth-utils'\n\nexport const GET = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: { id: string } }\n) => {\n  try {\n    const schedule = await prisma.schedule.findUnique({\n      where: { id: params.id },\n      include: {\n        assignee: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        creator: {\n          select: {\n            id: true,\n            name: true,\n          }\n        }\n      }\n    })\n\n    if (!schedule) {\n      return NextResponse.json(\n        { error: '排班未找到' },\n        { status: 404 }\n      )\n    }\n\n    return NextResponse.json(schedule)\n  } catch (error) {\n    console.error('Failed to fetch schedule:', error)\n    return NextResponse.json(\n      { error: '获取排班信息失败' },\n      { status: 500 }\n    )\n  }\n})\n\nexport const PATCH = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: { id: string } }\n) => {\n  try {\n    const {\n      title,\n      description,\n      startTime,\n      endTime,\n      location,\n      type,\n      status,\n      assignedTo,\n      color\n    } = await request.json()\n\n    const updateData: any = {}\n    if (title !== undefined) updateData.title = title\n    if (description !== undefined) updateData.description = description\n    if (startTime !== undefined) updateData.startTime = new Date(startTime)\n    if (endTime !== undefined) updateData.endTime = new Date(endTime)\n    if (location !== undefined) updateData.location = location\n    if (type !== undefined) updateData.type = type\n    if (status !== undefined) updateData.status = status\n    if (assignedTo !== undefined) updateData.assignedTo = assignedTo\n    if (color !== undefined) updateData.color = color\n\n    // 如果更新时间，检查冲突\n    if (startTime || endTime || assignedTo) {\n      const currentSchedule = await prisma.schedule.findUnique({\n        where: { id: params.id }\n      })\n\n      if (!currentSchedule) {\n        return NextResponse.json(\n          { error: '排班未找到' },\n          { status: 404 }\n        )\n      }\n\n      const checkStartTime = startTime ? new Date(startTime) : currentSchedule.startTime\n      const checkEndTime = endTime ? new Date(endTime) : currentSchedule.endTime\n      const checkAssignedTo = assignedTo !== undefined ? assignedTo : currentSchedule.assignedTo\n\n      if (checkAssignedTo) {\n        const conflictingSchedule = await prisma.schedule.findFirst({\n          where: {\n            id: { not: params.id },\n            assignedTo: checkAssignedTo,\n            status: 'ACTIVE',\n            OR: [\n              {\n                startTime: { lte: checkStartTime },\n                endTime: { gt: checkStartTime }\n              },\n              {\n                startTime: { lt: checkEndTime },\n                endTime: { gte: checkEndTime }\n              }\n            ]\n          }\n        })\n\n        if (conflictingSchedule) {\n          return NextResponse.json(\n            { error: '该时间段已有其他安排' },\n            { status: 400 }\n          )\n        }\n      }\n    }\n\n    const schedule = await prisma.schedule.update({\n      where: { id: params.id },\n      data: updateData,\n      include: {\n        assignee: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        creator: {\n          select: {\n            id: true,\n            name: true,\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(schedule)\n  } catch (error) {\n    console.error('Failed to update schedule:', error)\n    return NextResponse.json(\n      { error: '更新排班失败' },\n      { status: 500 }\n    )\n  }\n})\n\nexport const DELETE = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: { id: string } }\n) => {\n  try {\n    await prisma.schedule.delete({\n      where: { id: params.id }\n    })\n\n    return NextResponse.json({ message: '排班删除成功' })\n  } catch (error) {\n    console.error('Failed to delete schedule:', error)\n    return NextResponse.json(\n      { error: '删除排班失败' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC3B,SACA,SACA,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,QAAQ,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC7B,SACA,SACA,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EACJ,KAAK,EACL,WAAW,EACX,SAAS,EACT,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,UAAU,EACV,KAAK,EACN,GAAG,MAAM,QAAQ,IAAI;QAEtB,MAAM,aAAkB,CAAC;QACzB,IAAI,UAAU,WAAW,WAAW,KAAK,GAAG;QAC5C,IAAI,gBAAgB,WAAW,WAAW,WAAW,GAAG;QACxD,IAAI,cAAc,WAAW,WAAW,SAAS,GAAG,IAAI,KAAK;QAC7D,IAAI,YAAY,WAAW,WAAW,OAAO,GAAG,IAAI,KAAK;QACzD,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG;QAClD,IAAI,SAAS,WAAW,WAAW,IAAI,GAAG;QAC1C,IAAI,WAAW,WAAW,WAAW,MAAM,GAAG;QAC9C,IAAI,eAAe,WAAW,WAAW,UAAU,GAAG;QACtD,IAAI,UAAU,WAAW,WAAW,KAAK,GAAG;QAE5C,cAAc;QACd,IAAI,aAAa,WAAW,YAAY;YACtC,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACvD,OAAO;oBAAE,IAAI,OAAO,EAAE;gBAAC;YACzB;YAEA,IAAI,CAAC,iBAAiB;gBACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAQ,GACjB;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,iBAAiB,YAAY,IAAI,KAAK,aAAa,gBAAgB,SAAS;YAClF,MAAM,eAAe,UAAU,IAAI,KAAK,WAAW,gBAAgB,OAAO;YAC1E,MAAM,kBAAkB,eAAe,YAAY,aAAa,gBAAgB,UAAU;YAE1F,IAAI,iBAAiB;gBACnB,MAAM,sBAAsB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAC1D,OAAO;wBACL,IAAI;4BAAE,KAAK,OAAO,EAAE;wBAAC;wBACrB,YAAY;wBACZ,QAAQ;wBACR,IAAI;4BACF;gCACE,WAAW;oCAAE,KAAK;gCAAe;gCACjC,SAAS;oCAAE,IAAI;gCAAe;4BAChC;4BACA;gCACE,WAAW;oCAAE,IAAI;gCAAa;gCAC9B,SAAS;oCAAE,KAAK;gCAAa;4BAC/B;yBACD;oBACH;gBACF;gBAEA,IAAI,qBAAqB;oBACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBAAE,OAAO;oBAAa,GACtB;wBAAE,QAAQ;oBAAI;gBAElB;YACF;QACF;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,MAAM;YACN,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC9B,SACA,SACA,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;QACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAS;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
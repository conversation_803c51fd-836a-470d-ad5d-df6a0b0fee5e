module.exports = {

"[project]/.next-internal/server/app/api/admin/settings/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$next$2d$auth$2f$prisma$2d$adapter$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@next-auth/prisma-adapter/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
;
;
;
;
const authOptions = {
    adapter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$next$2d$auth$2f$prisma$2d$adapter$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrismaAdapter"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"]),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                    where: {
                        email: credentials.email
                    }
                });
                if (!user) {
                    return null;
                }
                const isPasswordValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.password);
                if (!isPasswordValid) {
                    return null;
                }
                return {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    username: user.username,
                    role: user.role,
                    avatar: user.avatar || undefined
                };
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.role = user.role;
                token.username = user.username;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
                session.user.username = token.username;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin'
    }
};
}}),
"[project]/src/lib/auth-utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSession": (()=>getSession),
    "requireAdmin": (()=>requireAdmin),
    "requireAuth": (()=>requireAuth),
    "withAdmin": (()=>withAdmin),
    "withAuth": (()=>withAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
;
;
async function getSession() {
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
}
async function requireAuth() {
    const session = await getSession();
    if (!session) {
        throw new Error('未授权访问');
    }
    return session;
}
async function requireAdmin() {
    const session = await requireAuth();
    if (session.user.role !== 'ADMIN') {
        throw new Error('需要管理员权限');
    }
    return session;
}
function withAuth(handler) {
    return async (request)=>{
        try {
            const session = await requireAuth();
            return await handler(request, session);
        } catch (error) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '未授权访问'
            }, {
                status: 401
            });
        }
    };
}
function withAdmin(handler) {
    return async (request)=>{
        try {
            const session = await requireAdmin();
            return await handler(request, session);
        } catch (error) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '需要管理员权限'
            }, {
                status: 403
            });
        }
    };
}
}}),
"[project]/src/app/api/admin/settings/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth-utils.ts [app-route] (ecmascript)");
;
;
;
// 默认系统设置
const defaultSettings = [
    // 基本信息
    {
        key: 'site_name',
        value: '创客教育平台',
        type: 'STRING',
        description: '网站名称',
        category: 'GENERAL'
    },
    {
        key: 'site_description',
        value: '专业的STEAM教育平台',
        type: 'STRING',
        description: '网站描述',
        category: 'GENERAL'
    },
    {
        key: 'site_slogan',
        value: '创新思维 · 实践能力',
        type: 'STRING',
        description: '网站标语',
        category: 'GENERAL'
    },
    {
        key: 'team_count',
        value: '50',
        type: 'NUMBER',
        description: '团队人数',
        category: 'GENERAL'
    },
    {
        key: 'copyright_year',
        value: '2024',
        type: 'STRING',
        description: '版权年份',
        category: 'GENERAL'
    },
    // 联系信息
    {
        key: 'contact_phone',
        value: '************',
        type: 'STRING',
        description: '联系电话',
        category: 'CONTACT'
    },
    {
        key: 'contact_email',
        value: '<EMAIL>',
        type: 'STRING',
        description: '联系邮箱',
        category: 'CONTACT'
    },
    {
        key: 'contact_address',
        value: '北京市海淀区创新大厦',
        type: 'STRING',
        description: '联系地址',
        category: 'CONTACT'
    },
    {
        key: 'contact_wechat',
        value: 'makeredu2024',
        type: 'STRING',
        description: '微信号',
        category: 'CONTACT'
    },
    {
        key: 'contact_qq',
        value: '123456789',
        type: 'STRING',
        description: 'QQ号',
        category: 'CONTACT'
    },
    // 功能开关
    {
        key: 'allow_registration',
        value: 'true',
        type: 'BOOLEAN',
        description: '允许用户注册',
        category: 'FEATURES'
    },
    {
        key: 'require_approval',
        value: 'true',
        type: 'BOOLEAN',
        description: '申请需要审批',
        category: 'FEATURES'
    },
    {
        key: 'maintenance_mode',
        value: 'false',
        type: 'BOOLEAN',
        description: '维护模式',
        category: 'FEATURES'
    },
    {
        key: 'show_team_section',
        value: 'true',
        type: 'BOOLEAN',
        description: '显示团队介绍',
        category: 'FEATURES'
    },
    {
        key: 'show_projects_section',
        value: 'true',
        type: 'BOOLEAN',
        description: '显示作品展示',
        category: 'FEATURES'
    },
    {
        key: 'show_blog_section',
        value: 'true',
        type: 'BOOLEAN',
        description: '显示博客功能',
        category: 'FEATURES'
    },
    {
        key: 'show_apply_section',
        value: 'true',
        type: 'BOOLEAN',
        description: '显示申请功能',
        category: 'FEATURES'
    },
    {
        key: 'enable_comments',
        value: 'true',
        type: 'BOOLEAN',
        description: '启用评论功能',
        category: 'FEATURES'
    },
    {
        key: 'enable_likes',
        value: 'true',
        type: 'BOOLEAN',
        description: '启用点赞功能',
        category: 'FEATURES'
    },
    // 文件设置
    {
        key: 'max_file_size',
        value: '10',
        type: 'NUMBER',
        description: '最大文件大小(MB)',
        category: 'FILES'
    },
    {
        key: 'allowed_file_types',
        value: 'jpg,jpeg,png,gif,pdf,doc,docx',
        type: 'STRING',
        description: '允许的文件类型',
        category: 'FILES'
    },
    // 外观设置
    {
        key: 'primary_color',
        value: '#3B82F6',
        type: 'STRING',
        description: '主题色',
        category: 'APPEARANCE'
    },
    {
        key: 'secondary_color',
        value: '#6366F1',
        type: 'STRING',
        description: '辅助色',
        category: 'APPEARANCE'
    },
    {
        key: 'hero_background',
        value: 'gradient',
        type: 'STRING',
        description: '首页背景样式',
        category: 'APPEARANCE'
    },
    {
        key: 'show_animations',
        value: 'true',
        type: 'BOOLEAN',
        description: '显示动画效果',
        category: 'APPEARANCE'
    },
    {
        key: 'dark_mode_enabled',
        value: 'false',
        type: 'BOOLEAN',
        description: '启用暗色模式',
        category: 'APPEARANCE'
    },
    // 首页内容
    {
        key: 'hero_title',
        value: '创新思维 · 实践能力',
        type: 'STRING',
        description: '首页标题',
        category: 'CONTENT'
    },
    {
        key: 'hero_subtitle',
        value: 'STEAM创客教育平台',
        type: 'STRING',
        description: '首页副标题',
        category: 'CONTENT'
    },
    {
        key: 'hero_description',
        value: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',
        type: 'STRING',
        description: '首页描述',
        category: 'CONTENT'
    },
    {
        key: 'about_intro',
        value: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',
        type: 'STRING',
        description: '关于我们简介',
        category: 'CONTENT'
    },
    // 统计数据
    {
        key: 'stats_projects',
        value: '500',
        type: 'NUMBER',
        description: '学生作品数',
        category: 'STATS'
    },
    {
        key: 'stats_students',
        value: '1000',
        type: 'NUMBER',
        description: '培训学员数',
        category: 'STATS'
    },
    {
        key: 'stats_satisfaction',
        value: '98',
        type: 'NUMBER',
        description: '满意度评价(%)',
        category: 'STATS'
    },
    // SEO设置
    {
        key: 'meta_keywords',
        value: 'STEAM教育,创客教育,3D打印,机器人,编程',
        type: 'STRING',
        description: 'SEO关键词',
        category: 'SEO'
    },
    {
        key: 'meta_description',
        value: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程',
        type: 'STRING',
        description: 'SEO描述',
        category: 'SEO'
    }
];
const GET = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withAdmin"])(async (request, session)=>{
    try {
        const { searchParams } = new URL(request.url);
        const category = searchParams.get('category');
        const where = category ? {
            category
        } : {};
        let settings = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].systemSetting.findMany({
            where,
            orderBy: {
                category: 'asc'
            }
        });
        // 如果没有设置，创建默认设置
        if (settings.length === 0) {
            const settingsToCreate = category ? defaultSettings.filter((s)=>s.category === category) : defaultSettings;
            for (const setting of settingsToCreate){
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].systemSetting.upsert({
                    where: {
                        key: setting.key
                    },
                    update: {},
                    create: setting
                });
            }
            settings = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].systemSetting.findMany({
                where,
                orderBy: {
                    category: 'asc'
                }
            });
        }
        // 转换为键值对格式
        const settingsMap = settings.reduce((acc, setting)=>{
            let value = setting.value;
            if (setting.type === 'BOOLEAN') {
                value = setting.value === 'true';
            } else if (setting.type === 'NUMBER') {
                value = parseFloat(setting.value);
            }
            acc[setting.key] = {
                value,
                type: setting.type,
                description: setting.description,
                category: setting.category
            };
            return acc;
        }, {});
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(settingsMap);
    } catch (error) {
        console.error('Failed to fetch settings:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '获取设置失败'
        }, {
            status: 500
        });
    }
});
const POST = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withAdmin"])(async (request, session)=>{
    try {
        const settings = await request.json();
        // 批量更新设置
        for (const [key, data] of Object.entries(settings)){
            const { value, type, description, category } = data;
            let stringValue = String(value);
            if (type === 'BOOLEAN') {
                stringValue = value ? 'true' : 'false';
            }
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].systemSetting.upsert({
                where: {
                    key
                },
                update: {
                    value: stringValue,
                    type,
                    description,
                    category
                },
                create: {
                    key,
                    value: stringValue,
                    type: type || 'STRING',
                    description,
                    category: category || 'GENERAL'
                }
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: '设置保存成功'
        });
    } catch (error) {
        console.error('Failed to save settings:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '保存设置失败'
        }, {
            status: 500
        });
    }
}) // 这个函数已经移动到 /api/settings/public/route.ts
;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__46ef8dfe._.js.map
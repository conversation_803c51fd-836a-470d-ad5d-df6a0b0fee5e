{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/projects/%5Bid%5D/view/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id: projectId } = await params\n    const session = await getServerSession(authOptions)\n    \n    // 获取客户端IP和User-Agent\n    const forwarded = request.headers.get('x-forwarded-for')\n    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown'\n    const userAgent = request.headers.get('user-agent') || 'unknown'\n\n    // 检查项目是否存在\n    const project = await prisma.project.findUnique({\n      where: { id: projectId }\n    })\n\n    if (!project) {\n      return NextResponse.json(\n        { error: '项目不存在' },\n        { status: 404 }\n      )\n    }\n\n    const userId = session?.user?.id\n\n    // 检查是否需要记录浏览\n    let shouldRecord = true\n\n    if (userId) {\n      // 登录用户：检查是否在最近1小时内浏览过\n      const recentView = await prisma.projectView.findFirst({\n        where: {\n          projectId,\n          userId,\n          createdAt: {\n            gte: new Date(Date.now() - 60 * 60 * 1000) // 1小时前\n          }\n        }\n      })\n      shouldRecord = !recentView\n    } else {\n      // 匿名用户：检查IP是否在最近1小时内浏览过\n      const recentView = await prisma.projectView.findFirst({\n        where: {\n          projectId,\n          ipAddress,\n          userId: null,\n          createdAt: {\n            gte: new Date(Date.now() - 60 * 60 * 1000) // 1小时前\n          }\n        }\n      })\n      shouldRecord = !recentView\n    }\n\n    if (shouldRecord) {\n      // 记录浏览\n      await prisma.projectView.create({\n        data: {\n          projectId,\n          userId,\n          ipAddress,\n          userAgent\n        }\n      })\n\n      // 更新浏览计数\n      const updatedProject = await prisma.project.update({\n        where: { id: projectId },\n        data: {\n          viewCount: {\n            increment: 1\n          }\n        }\n      })\n\n      return NextResponse.json({\n        viewCount: updatedProject.viewCount,\n        recorded: true\n      })\n    } else {\n      return NextResponse.json({\n        viewCount: project.viewCount,\n        recorded: false\n      })\n    }\n  } catch (error) {\n    console.error('记录浏览失败:', error)\n    return NextResponse.json(\n      { error: '操作失败，请稍后重试' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,IAAI,SAAS,EAAE,GAAG,MAAM;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,qBAAqB;QACrB,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,MAAM,YAAY,YAAY,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB;QAC5F,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAEvD,WAAW;QACX,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI;YAAU;QACzB;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,SAAS,MAAM;QAE9B,aAAa;QACb,IAAI,eAAe;QAEnB,IAAI,QAAQ;YACV,sBAAsB;YACtB,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBACpD,OAAO;oBACL;oBACA;oBACA,WAAW;wBACT,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,OAAO;oBACpD;gBACF;YACF;YACA,eAAe,CAAC;QAClB,OAAO;YACL,wBAAwB;YACxB,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBACpD,OAAO;oBACL;oBACA;oBACA,QAAQ;oBACR,WAAW;wBACT,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,OAAO;oBACpD;gBACF;YACF;YACA,eAAe,CAAC;QAClB;QAEA,IAAI,cAAc;YAChB,OAAO;YACP,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC9B,MAAM;oBACJ;oBACA;oBACA;oBACA;gBACF;YACF;YAEA,SAAS;YACT,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBACjD,OAAO;oBAAE,IAAI;gBAAU;gBACvB,MAAM;oBACJ,WAAW;wBACT,WAAW;oBACb;gBACF;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,WAAW,eAAe,SAAS;gBACnC,UAAU;YACZ;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,WAAW,QAAQ,SAAS;gBAC5B,UAAU;YACZ;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAa,GACtB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\n// 创建带有重试机制的Prisma客户端\nconst createPrismaClient = () => {\n  return new PrismaClient({\n    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],\n  })\n}\n\nexport const prisma = globalForPrisma.prisma ?? createPrismaClient()\n\nif (process.env.NODE_ENV !== 'production') {\n  globalForPrisma.prisma = prisma\n}\n\n// 确保数据库连接\nexport async function ensureDbConnection() {\n  try {\n    await prisma.$connect()\n    return true\n  } catch (error) {\n    console.error('Database connection failed:', error)\n    return false\n  }\n}\n\n// 安全的数据库操作包装器\nexport async function safeDbOperation<T>(operation: () => Promise<T>, fallback: T): Promise<T> {\n  try {\n    await ensureDbConnection()\n    return await operation()\n  } catch (error) {\n    console.error('Database operation failed:', error)\n    return fallback\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIxB,qBAAqB;AACrB,MAAM,qBAAqB;IACzB,OAAO,IAAI,6HAAA,CAAA,eAAY,CAAC;QACtB,KAAK,uCAAyC;YAAC;YAAS;SAAO;IACjE;AACF;AAEO,MAAM,SAAS,gBAAgB,MAAM,IAAI;AAEhD,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;AAC3B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,QAAQ;QACrB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,eAAe,gBAAmB,SAA2B,EAAE,QAAW;IAC/E,IAAI;QACF,MAAM;QACN,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, username, password, name } = await request.json()\n\n    // 验证必填字段\n    if (!email || !username || !password || !name) {\n      return NextResponse.json(\n        { error: '所有字段都是必填的' },\n        { status: 400 }\n      )\n    }\n\n    // 检查用户是否已存在\n    const existingUser = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { email },\n          { username }\n        ]\n      }\n    })\n\n    if (existingUser) {\n      return NextResponse.json(\n        { error: '用户名或邮箱已存在' },\n        { status: 400 }\n      )\n    }\n\n    // 加密密码\n    const hashedPassword = await bcrypt.hash(password, 12)\n\n    // 创建用户\n    const user = await prisma.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        name,\n      }\n    })\n\n    // 返回用户信息（不包含密码）\n    const { password: _, ...userWithoutPassword } = user\n\n    return NextResponse.json(\n      { \n        message: '用户注册成功',\n        user: userWithoutPassword \n      },\n      { status: 201 }\n    )\n  } catch (error) {\n    console.error('Registration error:', error)\n    return NextResponse.json(\n      { error: '注册失败，请稍后重试' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9D,SAAS;QACT,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL,IAAI;oBACF;wBAAE;oBAAM;oBACR;wBAAE;oBAAS;iBACZ;YACH;QACF;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;QAEnD,OAAO;QACP,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;YACF;QACF;QAEA,gBAAgB;QAChB,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,qBAAqB,GAAG;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,MAAM;QACR,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAa,GACtB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
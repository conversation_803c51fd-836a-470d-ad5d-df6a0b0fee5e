{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar ?? undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/projects/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { withAuth } from '@/lib/auth-utils'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  const { id } = await params\n  try {\n    const project = await prisma.project.findUnique({\n      where: {\n        id,\n        status: 'PUBLISHED'\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        comments: {\n          include: {\n            author: {\n              select: {\n                id: true,\n                name: true,\n                avatar: true,\n              }\n            }\n          },\n          orderBy: {\n            createdAt: 'desc'\n          }\n        }\n      }\n    })\n\n    if (!project) {\n      return NextResponse.json(\n        { error: '作品未找到' },\n        { status: 404 }\n      )\n    }\n\n    // 注意：浏览计数现在通过专门的 /view 端点处理\n\n    // 解析JSON字符串为数组\n    const processedProject = {\n      ...project,\n      images: project.images ? JSON.parse(project.images) : [],\n      tags: project.tags ? JSON.parse(project.tags) : []\n    }\n\n    return NextResponse.json(processedProject)\n  } catch (error) {\n    console.error('Failed to fetch project:', error)\n    return NextResponse.json(\n      { error: '获取作品详情失败' },\n      { status: 500 }\n    )\n  }\n}\n\nexport const PUT = withAuth(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  const { id } = await params\n  try {\n    const {\n      title,\n      description,\n      content,\n      images,\n      category,\n      tags,\n      featured\n    } = await request.json()\n\n    // 检查作品是否存在且用户有权限编辑\n    const existingProject = await prisma.project.findUnique({\n      where: { id }\n    })\n\n    if (!existingProject) {\n      return NextResponse.json(\n        { error: '作品未找到' },\n        { status: 404 }\n      )\n    }\n\n    if (existingProject.authorId !== session.user.id && session.user.role !== 'ADMIN') {\n      return NextResponse.json(\n        { error: '无权限编辑此作品' },\n        { status: 403 }\n      )\n    }\n\n    const project = await prisma.project.update({\n      where: { id },\n      data: {\n        title,\n        description,\n        content,\n        images,\n        category,\n        tags,\n        featured\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(project)\n  } catch (error) {\n    console.error('Failed to update project:', error)\n    return NextResponse.json(\n      { error: '更新作品失败' },\n      { status: 500 }\n    )\n  }\n})\n\nexport const DELETE = withAuth(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  const { id } = await params\n  try {\n    // 检查作品是否存在且用户有权限删除\n    const existingProject = await prisma.project.findUnique({\n      where: { id }\n    })\n\n    if (!existingProject) {\n      return NextResponse.json(\n        { error: '作品未找到' },\n        { status: 404 }\n      )\n    }\n\n    if (existingProject.authorId !== session.user.id && session.user.role !== 'ADMIN') {\n      return NextResponse.json(\n        { error: '无权限删除此作品' },\n        { status: 403 }\n      )\n    }\n\n    await prisma.project.delete({\n      where: { id }\n    })\n\n    return NextResponse.json({ message: '作品删除成功' })\n  } catch (error) {\n    console.error('Failed to delete project:', error)\n    return NextResponse.json(\n      { error: '删除作品失败' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,IAAI;QACF,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBACL;gBACA,QAAQ;YACV;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,QAAQ;4BACN,QAAQ;gCACN,IAAI;gCACJ,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;oBACA,SAAS;wBACP,WAAW;oBACb;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAE5B,eAAe;QACf,MAAM,mBAAmB;YACvB,GAAG,OAAO;YACV,QAAQ,QAAQ,MAAM,GAAG,KAAK,KAAK,CAAC,QAAQ,MAAM,IAAI,EAAE;YACxD,MAAM,QAAQ,IAAI,GAAG,KAAK,KAAK,CAAC,QAAQ,IAAI,IAAI,EAAE;QACpD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,WAAQ,AAAD,EAAE,OAC1B,SACA,SACA,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,IAAI;QACF,MAAM,EACJ,KAAK,EACL,WAAW,EACX,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ,IAAI;QAEtB,mBAAmB;QACnB,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,gBAAgB,QAAQ,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACjF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAW,GACpB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,OAAO;gBAAE;YAAG;YACZ,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,WAAQ,AAAD,EAAE,OAC7B,SACA,SACA,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,IAAI;QACF,mBAAmB;QACnB,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,gBAAgB,QAAQ,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACjF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAW,GACpB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,OAAO;gBAAE;YAAG;QACd;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAS;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar ?? undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session, context)\n    } catch (error) {\n      console.error('Admin auth error:', error)\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/admin/schedules/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { withAdmin } from '@/lib/auth-utils'\n\nexport const GET = withAdmin(async (request: NextRequest, session: any) => {\n  try {\n    const { searchParams } = new URL(request.url)\n    const type = searchParams.get('type')\n    const status = searchParams.get('status')\n    const startDate = searchParams.get('startDate')\n    const endDate = searchParams.get('endDate')\n\n    const where: any = {}\n\n    if (type && type !== 'ALL') {\n      where.type = type\n    }\n\n    if (status && status !== 'ALL') {\n      where.status = status\n    }\n\n    if (startDate && endDate) {\n      where.startTime = {\n        gte: new Date(startDate),\n        lte: new Date(endDate)\n      }\n    }\n\n    const schedules = await prisma.schedule.findMany({\n      where,\n      include: {\n        assignee: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        creator: {\n          select: {\n            id: true,\n            name: true,\n          }\n        }\n      },\n      orderBy: {\n        startTime: 'asc'\n      }\n    })\n\n    return NextResponse.json(schedules)\n  } catch (error) {\n    console.error('Failed to fetch schedules:', error)\n    return NextResponse.json(\n      { error: '获取排班列表失败' },\n      { status: 500 }\n    )\n  }\n})\n\nexport const POST = withAdmin(async (request: NextRequest, session: any) => {\n  try {\n    const {\n      title,\n      description,\n      startTime,\n      endTime,\n      location,\n      type,\n      assignedTo,\n      color,\n      isRecurring,\n      recurringPattern\n    } = await request.json()\n\n    if (!title || !startTime || !endTime || !type) {\n      return NextResponse.json(\n        { error: '标题、开始时间、结束时间和类型为必填项' },\n        { status: 400 }\n      )\n    }\n\n    // 检查时间冲突\n    const conflictingSchedule = await prisma.schedule.findFirst({\n      where: {\n        assignedTo,\n        status: 'ACTIVE',\n        OR: [\n          {\n            startTime: {\n              lte: new Date(startTime)\n            },\n            endTime: {\n              gt: new Date(startTime)\n            }\n          },\n          {\n            startTime: {\n              lt: new Date(endTime)\n            },\n            endTime: {\n              gte: new Date(endTime)\n            }\n          }\n        ]\n      }\n    })\n\n    if (conflictingSchedule) {\n      return NextResponse.json(\n        { error: '该时间段已有其他安排' },\n        { status: 400 }\n      )\n    }\n\n    // 验证assignedTo用户是否存在\n    if (assignedTo) {\n      const assignedUser = await prisma.user.findUnique({\n        where: { id: assignedTo }\n      })\n\n      if (!assignedUser) {\n        return NextResponse.json(\n          { error: '指定的用户不存在' },\n          { status: 400 }\n        )\n      }\n    }\n\n    const schedule = await prisma.schedule.create({\n      data: {\n        title,\n        description,\n        startTime: new Date(startTime),\n        endTime: new Date(endTime),\n        location,\n        type,\n        assignedTo: assignedTo || null,\n        color: color || '#3B82F6',\n        isRecurring: isRecurring || false,\n        recurringPattern: recurringPattern ? JSON.stringify(recurringPattern) : null,\n        createdBy: session.user.id\n      },\n      include: {\n        assignee: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        creator: {\n          select: {\n            id: true,\n            name: true,\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(schedule, { status: 201 })\n  } catch (error) {\n    console.error('Failed to create schedule:', error)\n    return NextResponse.json(\n      { error: '创建排班失败' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAsB;IACxD,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ,SAAS,OAAO;YAC1B,MAAM,IAAI,GAAG;QACf;QAEA,IAAI,UAAU,WAAW,OAAO;YAC9B,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,aAAa,SAAS;YACxB,MAAM,SAAS,GAAG;gBAChB,KAAK,IAAI,KAAK;gBACd,KAAK,IAAI,KAAK;YAChB;QACF;QAEA,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,OAAO,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAsB;IACzD,IAAI;QACF,MAAM,EACJ,KAAK,EACL,WAAW,EACX,SAAS,EACT,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,KAAK,EACL,WAAW,EACX,gBAAgB,EACjB,GAAG,MAAM,QAAQ,IAAI;QAEtB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,sBAAsB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC1D,OAAO;gBACL;gBACA,QAAQ;gBACR,IAAI;oBACF;wBACE,WAAW;4BACT,KAAK,IAAI,KAAK;wBAChB;wBACA,SAAS;4BACP,IAAI,IAAI,KAAK;wBACf;oBACF;oBACA;wBACE,WAAW;4BACT,IAAI,IAAI,KAAK;wBACf;wBACA,SAAS;4BACP,KAAK,IAAI,KAAK;wBAChB;oBACF;iBACD;YACH;QACF;QAEA,IAAI,qBAAqB;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAa,GACtB;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,YAAY;YACd,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAChD,OAAO;oBAAE,IAAI;gBAAW;YAC1B;YAEA,IAAI,CAAC,cAAc;gBACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAW,GACpB;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ;gBACA;gBACA,WAAW,IAAI,KAAK;gBACpB,SAAS,IAAI,KAAK;gBAClB;gBACA;gBACA,YAAY,cAAc;gBAC1B,OAAO,SAAS;gBAChB,aAAa,eAAe;gBAC5B,kBAAkB,mBAAmB,KAAK,SAAS,CAAC,oBAAoB;gBACxE,WAAW,QAAQ,IAAI,CAAC,EAAE;YAC5B;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
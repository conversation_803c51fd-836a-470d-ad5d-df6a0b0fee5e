{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\n// 创建带有重试机制的Prisma客户端\nconst createPrismaClient = () => {\n  return new PrismaClient({\n    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],\n  })\n}\n\nexport const prisma = globalForPrisma.prisma ?? createPrismaClient()\n\nif (process.env.NODE_ENV !== 'production') {\n  globalForPrisma.prisma = prisma\n}\n\n// 确保数据库连接\nexport async function ensureDbConnection() {\n  try {\n    await prisma.$connect()\n    return true\n  } catch (error) {\n    console.error('Database connection failed:', error)\n    return false\n  }\n}\n\n// 安全的数据库操作包装器\nexport async function safeDbOperation<T>(operation: () => Promise<T>, fallback: T): Promise<T> {\n  try {\n    await ensureDbConnection()\n    return await operation()\n  } catch (error) {\n    console.error('Database operation failed:', error)\n    return fallback\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIxB,qBAAqB;AACrB,MAAM,qBAAqB;IACzB,OAAO,IAAI,6HAAA,CAAA,eAAY,CAAC;QACtB,KAAK,uCAAyC;YAAC;YAAS;SAAO;IACjE;AACF;AAEO,MAAM,SAAS,gBAAgB,MAAM,IAAI;AAEhD,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;AAC3B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,QAAQ;QACrB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,eAAe,gBAAmB,SAA2B,EAAE,QAAW;IAC/E,IAAI;QACF,MAAM;QACN,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar ?? undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session, context)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session, context)\n    } catch (error) {\n      console.error('Admin auth error:', error)\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/admin/applications/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { safeDbOperation } from '@/lib/prisma'\nimport { withAdmin } from '@/lib/auth-utils'\n\nexport const GET = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  const { id } = await params\n\n  const application = await safeDbOperation(async () => {\n    const { prisma } = await import('@/lib/prisma')\n    return await prisma.application.findUnique({\n      where: { id },\n      include: {\n        applicant: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n  }, null)\n\n  if (!application) {\n    return NextResponse.json(\n      { error: '申请未找到' },\n      { status: 404 }\n    )\n  }\n\n  // 解析JSON字符串为数组\n  const processedApplication = {\n    ...application,\n    skills: application.skills ? JSON.parse(application.skills) : []\n  }\n\n  return NextResponse.json(processedApplication)\n})\n\nexport const PATCH = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  const { id } = await params\n  const { status, reviewNote } = await request.json()\n\n  if (!['APPROVED', 'REJECTED'].includes(status)) {\n    return NextResponse.json(\n      { error: '无效的审核状态' },\n      { status: 400 }\n    )\n  }\n\n  const result = await safeDbOperation(async () => {\n    const { prisma } = await import('@/lib/prisma')\n\n    const application = await prisma.application.update({\n      where: { id },\n      data: {\n        status,\n        reviewNote,\n        reviewedBy: session.user.id,\n        reviewedAt: new Date()\n      },\n      include: {\n        applicant: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    // 如果申请通过，自动创建团队成员记录\n    if (status === 'APPROVED') {\n      try {\n        // 检查是否已经是团队成员\n        const existingMember = await prisma.teamMember.findFirst({\n          where: {\n            OR: [\n              { userId: application.applicantId },\n              { name: application.name }\n            ]\n          }\n        })\n\n        if (!existingMember) {\n          // 自动创建团队成员记录\n          await prisma.teamMember.create({\n            data: {\n              userId: application.applicantId,\n              name: application.name,\n              position: application.position,\n              bio: application.motivation,\n              skills: application.skills, // 已经是JSON字符串\n              isActive: true,\n              displayOrder: 0,\n              joinDate: new Date()\n            }\n          })\n\n          console.log(`申请通过，自动创建团队成员: ${application.name}`)\n        }\n      } catch (error) {\n        console.error('自动创建团队成员失败:', error)\n        // 不影响申请审核的主流程\n      }\n    }\n\n    return application\n  }, null)\n\n  if (!result) {\n    return NextResponse.json(\n      { error: '审核申请失败' },\n      { status: 500 }\n    )\n  }\n\n  // 解析JSON字符串为数组\n  const processedApplication = {\n    ...result,\n    skills: result.skills ? JSON.parse(result.skills) : []\n  }\n\n  return NextResponse.json(processedApplication)\n})\n\nexport const DELETE = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  const { id } = await params\n\n  const result = await safeDbOperation(async () => {\n    const { prisma } = await import('@/lib/prisma')\n    await prisma.application.delete({\n      where: { id }\n    })\n    return true\n  }, false)\n\n  if (result) {\n    return NextResponse.json({ message: '申请删除成功' })\n  } else {\n    return NextResponse.json(\n      { error: '删除申请失败' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC3B,SACA,SACA,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,MAAM,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACxC,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,OAAO,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;YACzC,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;YACF;QACF;IACF,GAAG;IAEH,IAAI,CAAC,aAAa;QAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;IAEA,eAAe;IACf,MAAM,uBAAuB;QAC3B,GAAG,WAAW;QACd,QAAQ,YAAY,MAAM,GAAG,KAAK,KAAK,CAAC,YAAY,MAAM,IAAI,EAAE;IAClE;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B;AAEO,MAAM,QAAQ,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC7B,SACA,SACA,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ,IAAI;IAEjD,IAAI,CAAC;QAAC;QAAY;KAAW,CAAC,QAAQ,CAAC,SAAS;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAU,GACnB;YAAE,QAAQ;QAAI;IAElB;IAEA,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACnC,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,MAAM,cAAc,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;YAClD,OAAO;gBAAE;YAAG;YACZ,MAAM;gBACJ;gBACA;gBACA,YAAY,QAAQ,IAAI,CAAC,EAAE;gBAC3B,YAAY,IAAI;YAClB;YACA,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,WAAW,YAAY;YACzB,IAAI;gBACF,cAAc;gBACd,MAAM,iBAAiB,MAAM,OAAO,UAAU,CAAC,SAAS,CAAC;oBACvD,OAAO;wBACL,IAAI;4BACF;gCAAE,QAAQ,YAAY,WAAW;4BAAC;4BAClC;gCAAE,MAAM,YAAY,IAAI;4BAAC;yBAC1B;oBACH;gBACF;gBAEA,IAAI,CAAC,gBAAgB;oBACnB,aAAa;oBACb,MAAM,OAAO,UAAU,CAAC,MAAM,CAAC;wBAC7B,MAAM;4BACJ,QAAQ,YAAY,WAAW;4BAC/B,MAAM,YAAY,IAAI;4BACtB,UAAU,YAAY,QAAQ;4BAC9B,KAAK,YAAY,UAAU;4BAC3B,QAAQ,YAAY,MAAM;4BAC1B,UAAU;4BACV,cAAc;4BACd,UAAU,IAAI;wBAChB;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,IAAI,EAAE;gBAClD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,cAAc;YAChB;QACF;QAEA,OAAO;IACT,GAAG;IAEH,IAAI,CAAC,QAAQ;QACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;IAEA,eAAe;IACf,MAAM,uBAAuB;QAC3B,GAAG,MAAM;QACT,QAAQ,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,OAAO,MAAM,IAAI,EAAE;IACxD;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B;AAEO,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC9B,SACA,SACA,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACnC,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;YAC9B,OAAO;gBAAE;YAAG;QACd;QACA,OAAO;IACT,GAAG;IAEH,IAAI,QAAQ;QACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAS;IAC/C,OAAO;QACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
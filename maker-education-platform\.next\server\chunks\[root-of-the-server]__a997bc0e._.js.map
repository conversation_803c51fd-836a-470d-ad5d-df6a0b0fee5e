{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/admin/applications/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { withAdmin } from '@/lib/auth-utils'\n\nexport const GET = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: { id: string } }\n) => {\n  try {\n    const application = await prisma.application.findUnique({\n      where: { id: params.id },\n      include: {\n        applicant: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    if (!application) {\n      return NextResponse.json(\n        { error: '申请未找到' },\n        { status: 404 }\n      )\n    }\n\n    // 解析JSON字符串为数组\n    const processedApplication = {\n      ...application,\n      skills: application.skills ? JSON.parse(application.skills) : []\n    }\n\n    return NextResponse.json(processedApplication)\n  } catch (error) {\n    console.error('Failed to fetch application:', error)\n    return NextResponse.json(\n      { error: '获取申请详情失败' },\n      { status: 500 }\n    )\n  }\n})\n\nexport const PATCH = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: { id: string } }\n) => {\n  try {\n    const { status, reviewNote } = await request.json()\n\n    if (!['APPROVED', 'REJECTED'].includes(status)) {\n      return NextResponse.json(\n        { error: '无效的审核状态' },\n        { status: 400 }\n      )\n    }\n\n    const application = await prisma.application.update({\n      where: { id: params.id },\n      data: {\n        status,\n        reviewNote,\n        reviewedBy: session.user.id,\n        reviewedAt: new Date()\n      },\n      include: {\n        applicant: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    // 如果申请通过，可以考虑自动创建团队成员记录\n    if (status === 'APPROVED') {\n      // 这里可以添加自动创建团队成员的逻辑\n      // 或者发送通知邮件等\n    }\n\n    // 解析JSON字符串为数组\n    const processedApplication = {\n      ...application,\n      skills: application.skills ? JSON.parse(application.skills) : []\n    }\n\n    return NextResponse.json(processedApplication)\n  } catch (error) {\n    console.error('Failed to review application:', error)\n    return NextResponse.json(\n      { error: '审核申请失败' },\n      { status: 500 }\n    )\n  }\n})\n\nexport const DELETE = withAdmin(async (\n  request: NextRequest,\n  session: any,\n  { params }: { params: { id: string } }\n) => {\n  try {\n    await prisma.application.delete({\n      where: { id: params.id }\n    })\n\n    return NextResponse.json({ message: '申请删除成功' })\n  } catch (error) {\n    console.error('Failed to delete application:', error)\n    return NextResponse.json(\n      { error: '删除申请失败' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC3B,SACA,SACA,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,uBAAuB;YAC3B,GAAG,WAAW;YACd,QAAQ,YAAY,MAAM,GAAG,KAAK,KAAK,CAAC,YAAY,MAAM,IAAI,EAAE;QAClE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,QAAQ,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC7B,SACA,SACA,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEjD,IAAI,CAAC;YAAC;YAAY;SAAW,CAAC,QAAQ,CAAC,SAAS;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,MAAM;gBACJ;gBACA;gBACA,YAAY,QAAQ,IAAI,CAAC,EAAE;gBAC3B,YAAY,IAAI;YAClB;YACA,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,wBAAwB;QACxB,IAAI,WAAW,YAAY;QACzB,oBAAoB;QACpB,YAAY;QACd;QAEA,eAAe;QACf,MAAM,uBAAuB;YAC3B,GAAG,WAAW;YACd,QAAQ,YAAY,MAAM,GAAG,KAAK,KAAK,CAAC,YAAY,MAAM,IAAI,EAAE;QAClE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAC9B,SACA,SACA,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAC9B,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;QACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAS;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/contact/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { name, email, phone, subject, message } = await request.json()\n\n    // 验证必填字段\n    if (!name || !email || !subject || !message) {\n      return NextResponse.json(\n        { error: '请填写所有必填字段' },\n        { status: 400 }\n      )\n    }\n\n    // 验证邮箱格式\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    if (!emailRegex.test(email)) {\n      return NextResponse.json(\n        { error: '请输入有效的邮箱地址' },\n        { status: 400 }\n      )\n    }\n\n    // 保存联系信息到数据库\n    const contact = await prisma.contact.create({\n      data: {\n        name,\n        email,\n        phone: phone || null,\n        subject,\n        message,\n        status: 'PENDING',\n        createdAt: new Date()\n      }\n    })\n\n    // 这里可以添加发送邮件通知的逻辑\n    // await sendNotificationEmail(contact)\n\n    return NextResponse.json({\n      message: '消息发送成功，我们会尽快回复您',\n      id: contact.id\n    })\n  } catch (error) {\n    console.error('Failed to save contact message:', error)\n    return NextResponse.json(\n      { error: '发送失败，请稍后重试' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEnE,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAa,GACtB;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ;gBACA;gBACA,OAAO,SAAS;gBAChB;gBACA;gBACA,QAAQ;gBACR,WAAW,IAAI;YACjB;QACF;QAEA,kBAAkB;QAClB,uCAAuC;QAEvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,IAAI,QAAQ,EAAE;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAa,GACtB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
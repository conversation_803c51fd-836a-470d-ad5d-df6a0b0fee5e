{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar ?? undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/applications/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { withAuth, withAdmin } from '@/lib/auth-utils'\n\nexport const GET = withAdmin(async (request: NextRequest, session: any) => {\n  try {\n    const { searchParams } = new URL(request.url)\n    const status = searchParams.get('status')\n    const page = searchParams.get('page') || '1'\n    const limit = searchParams.get('limit') || '10'\n\n    const pageSize = parseInt(limit)\n    const skip = (parseInt(page) - 1) * pageSize\n\n    const where: any = {}\n    if (status && status !== 'ALL') {\n      where.status = status\n    }\n\n    const applications = await prisma.application.findMany({\n      where,\n      include: {\n        applicant: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      },\n      skip,\n      take: pageSize,\n    })\n\n    // 解析JSON字符串为数组\n    const processedApplications = applications.map(application => ({\n      ...application,\n      skills: application.skills ? JSON.parse(application.skills) : []\n    }))\n\n    const total = await prisma.application.count({ where })\n\n    return NextResponse.json({\n      applications: processedApplications,\n      pagination: {\n        page: parseInt(page),\n        pageSize,\n        total,\n        totalPages: Math.ceil(total / pageSize)\n      }\n    })\n  } catch (error) {\n    console.error('Failed to fetch applications:', error)\n    return NextResponse.json(\n      { error: '获取申请列表失败' },\n      { status: 500 }\n    )\n  }\n})\n\nexport const POST = withAuth(async (request: NextRequest, session: any) => {\n  try {\n    const {\n      name,\n      email,\n      phone,\n      position,\n      skills,\n      experience,\n      motivation,\n      resume\n    } = await request.json()\n\n    // 验证必填字段\n    if (!name || !email || !position || !motivation) {\n      return NextResponse.json(\n        { error: '姓名、邮箱、职位和申请动机为必填项' },\n        { status: 400 }\n      )\n    }\n\n    if (!skills || skills.length === 0) {\n      return NextResponse.json(\n        { error: '请至少选择一项技能' },\n        { status: 400 }\n      )\n    }\n\n    // 检查是否已有待审核的申请\n    const existingApplication = await prisma.application.findFirst({\n      where: {\n        applicantId: session.user.id,\n        status: 'PENDING'\n      }\n    })\n\n    if (existingApplication) {\n      return NextResponse.json(\n        { error: '您已有待审核的申请，请等待审核结果' },\n        { status: 400 }\n      )\n    }\n\n    const application = await prisma.application.create({\n      data: {\n        applicantId: session.user.id,\n        name,\n        email,\n        phone,\n        position,\n        skills: JSON.stringify(skills),\n        experience,\n        motivation,\n        resume,\n        status: 'PENDING'\n      },\n      include: {\n        applicant: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    // 解析JSON字符串为数组\n    const processedApplication = {\n      ...application,\n      skills: application.skills ? JSON.parse(application.skills) : []\n    }\n\n    return NextResponse.json(processedApplication, { status: 201 })\n  } catch (error) {\n    console.error('Failed to create application:', error)\n    return NextResponse.json(\n      { error: '提交申请失败' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAsB;IACxD,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;QACzC,MAAM,QAAQ,aAAa,GAAG,CAAC,YAAY;QAE3C,MAAM,WAAW,SAAS;QAC1B,MAAM,OAAO,CAAC,SAAS,QAAQ,CAAC,IAAI;QAEpC,MAAM,QAAa,CAAC;QACpB,IAAI,UAAU,WAAW,OAAO;YAC9B,MAAM,MAAM,GAAG;QACjB;QAEA,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACrD;YACA,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;YACA;YACA,MAAM;QACR;QAEA,eAAe;QACf,MAAM,wBAAwB,aAAa,GAAG,CAAC,CAAA,cAAe,CAAC;gBAC7D,GAAG,WAAW;gBACd,QAAQ,YAAY,MAAM,GAAG,KAAK,KAAK,CAAC,YAAY,MAAM,IAAI,EAAE;YAClE,CAAC;QAED,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,CAAC;YAAE;QAAM;QAErD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,cAAc;YACd,YAAY;gBACV,MAAM,SAAS;gBACf;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,QAAQ;YAChC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,OAAO,CAAA,GAAA,6HAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,SAAsB;IACxD,IAAI;QACF,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,QAAQ,EACR,MAAM,EACN,UAAU,EACV,UAAU,EACV,MAAM,EACP,GAAG,MAAM,QAAQ,IAAI;QAEtB,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,sBAAsB,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;YAC7D,OAAO;gBACL,aAAa,QAAQ,IAAI,CAAC,EAAE;gBAC5B,QAAQ;YACV;QACF;QAEA,IAAI,qBAAqB;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,MAAM;gBACJ,aAAa,QAAQ,IAAI,CAAC,EAAE;gBAC5B;gBACA;gBACA;gBACA;gBACA,QAAQ,KAAK,SAAS,CAAC;gBACvB;gBACA;gBACA;gBACA,QAAQ;YACV;YACA,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,eAAe;QACf,MAAM,uBAAuB;YAC3B,GAAG,WAAW;YACd,QAAQ,YAAY,MAAM,GAAG,KAAK,KAAK,CAAC,YAAY,MAAM,IAAI,EAAE;QAClE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,sBAAsB;YAAE,QAAQ;QAAI;IAC/D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
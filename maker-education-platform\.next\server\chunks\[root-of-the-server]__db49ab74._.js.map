{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar ?? undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session, context)\n    } catch (error) {\n      console.error('Admin auth error:', error)\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/admin/analytics/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { withAdmin } from '@/lib/auth-utils'\n\nexport const GET = withAdmin(async (request: NextRequest, session: any) => {\n  try {\n    const { searchParams } = new URL(request.url)\n    const timeRange = searchParams.get('timeRange') || '7d'\n    \n    // 计算时间范围\n    const now = new Date()\n    let startDate: Date\n    \n    switch (timeRange) {\n      case '1d':\n        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)\n        break\n      case '7d':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)\n        break\n      case '30d':\n        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)\n        break\n      case '90d':\n        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)\n        break\n      default:\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)\n    }\n\n    // 获取基础统计数据\n    const [\n      totalUsers,\n      totalProjects,\n      totalPosts,\n      totalApplications,\n      totalViews,\n      userGrowth,\n      projectGrowth,\n      postGrowth,\n      popularProjects,\n      popularPosts,\n      recentActivity\n    ] = await Promise.all([\n      // 总用户数\n      prisma.user.count({\n        where: { isActive: true }\n      }),\n      \n      // 总项目数\n      prisma.project.count({\n        where: { status: 'PUBLISHED' }\n      }),\n      \n      // 总文章数\n      prisma.post.count({\n        where: { published: true }\n      }),\n      \n      // 总申请数\n      prisma.application.count(),\n      \n      // 总浏览量（项目浏览量之和）\n      prisma.project.aggregate({\n        _sum: {\n          viewCount: true\n        },\n        where: { status: 'PUBLISHED' }\n      }).then(result => result._sum.viewCount || 0),\n      \n      // 用户增长（时间范围内新增用户）\n      prisma.user.count({\n        where: {\n          createdAt: {\n            gte: startDate\n          },\n          isActive: true\n        }\n      }),\n      \n      // 项目增长（时间范围内新增项目）\n      prisma.project.count({\n        where: {\n          createdAt: {\n            gte: startDate\n          },\n          status: 'PUBLISHED'\n        }\n      }),\n      \n      // 文章增长（时间范围内新增文章）\n      prisma.post.count({\n        where: {\n          createdAt: {\n            gte: startDate\n          },\n          published: true\n        }\n      }),\n      \n      // 热门项目（按浏览量排序）\n      prisma.project.findMany({\n        where: { status: 'PUBLISHED' },\n        select: {\n          id: true,\n          title: true,\n          viewCount: true,\n          category: true,\n          createdAt: true\n        },\n        orderBy: {\n          viewCount: 'desc'\n        },\n        take: 10\n      }),\n      \n      // 热门文章（按浏览量排序）\n      prisma.post.findMany({\n        where: { published: true },\n        select: {\n          id: true,\n          title: true,\n          viewCount: true,\n          createdAt: true,\n          author: {\n            select: {\n              name: true\n            }\n          }\n        },\n        orderBy: {\n          viewCount: 'desc'\n        },\n        take: 10\n      }),\n      \n      // 最近活动（最近的用户注册、项目发布、文章发布等）\n      Promise.all([\n        // 最近用户注册\n        prisma.user.findMany({\n          where: {\n            createdAt: {\n              gte: startDate\n            },\n            isActive: true\n          },\n          select: {\n            name: true,\n            createdAt: true\n          },\n          orderBy: {\n            createdAt: 'desc'\n          },\n          take: 5\n        }).then(users => users.map(user => ({\n          type: 'user_registration',\n          description: `新用户 ${user.name} 注册`,\n          timestamp: user.createdAt.toISOString()\n        }))),\n        \n        // 最近项目发布\n        prisma.project.findMany({\n          where: {\n            createdAt: {\n              gte: startDate\n            },\n            status: 'PUBLISHED'\n          },\n          select: {\n            title: true,\n            createdAt: true,\n            author: {\n              select: {\n                name: true\n              }\n            }\n          },\n          orderBy: {\n            createdAt: 'desc'\n          },\n          take: 5\n        }).then(projects => projects.map(project => ({\n          type: 'project_published',\n          description: `${project.author.name} 发布了项目 \"${project.title}\"`,\n          timestamp: project.createdAt.toISOString()\n        }))),\n        \n        // 最近文章发布\n        prisma.post.findMany({\n          where: {\n            createdAt: {\n              gte: startDate\n            },\n            published: true\n          },\n          select: {\n            title: true,\n            createdAt: true,\n            author: {\n              select: {\n                name: true\n              }\n            }\n          },\n          orderBy: {\n            createdAt: 'desc'\n          },\n          take: 5\n        }).then(posts => posts.map(post => ({\n          type: 'post_published',\n          description: `${post.author.name} 发布了文章 \"${post.title}\"`,\n          timestamp: post.createdAt.toISOString()\n        })))\n      ]).then(activities => {\n        // 合并所有活动并按时间排序\n        const allActivities = activities.flat()\n        return allActivities.sort((a, b) => \n          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()\n        ).slice(0, 20)\n      })\n    ])\n\n    // 计算增长率\n    const calculateGrowthRate = (current: number, growth: number) => {\n      if (current === 0) return 0\n      const previous = current - growth\n      if (previous === 0) return growth > 0 ? 100 : 0\n      return Math.round((growth / previous) * 100)\n    }\n\n    const analyticsData = {\n      totalUsers,\n      totalProjects,\n      totalPosts,\n      totalApplications,\n      totalViews,\n      userGrowth: calculateGrowthRate(totalUsers, userGrowth),\n      projectGrowth: calculateGrowthRate(totalProjects, projectGrowth),\n      postGrowth: calculateGrowthRate(totalPosts, postGrowth),\n      popularProjects,\n      popularPosts,\n      recentActivity,\n      timeRange,\n      // 额外的统计信息\n      stats: {\n        avgProjectViews: totalProjects > 0 ? Math.round(totalViews / totalProjects) : 0,\n        activeUsersToday: userGrowth, // 今日新增用户作为活跃用户的近似值\n        publishedContentToday: projectGrowth + postGrowth\n      }\n    }\n\n    return NextResponse.json(analyticsData)\n  } catch (error) {\n    console.error('Failed to fetch analytics:', error)\n    return NextResponse.json(\n      { error: '获取分析数据失败' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAsB;IACxD,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,SAAS;QACT,MAAM,MAAM,IAAI;QAChB,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK;gBACpD;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;gBACxD;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;gBACzD;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;gBACzD;YACF;gBACE,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;QAC5D;QAEA,WAAW;QACX,MAAM,CACJ,YACA,eACA,YACA,mBACA,YACA,YACA,eACA,YACA,iBACA,cACA,eACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,UAAU;gBAAK;YAC1B;YAEA,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACnB,OAAO;oBAAE,QAAQ;gBAAY;YAC/B;YAEA,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,WAAW;gBAAK;YAC3B;YAEA,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK;YAExB,gBAAgB;YAChB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACvB,MAAM;oBACJ,WAAW;gBACb;gBACA,OAAO;oBAAE,QAAQ;gBAAY;YAC/B,GAAG,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,CAAC,SAAS,IAAI;YAE3C,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBACL,WAAW;wBACT,KAAK;oBACP;oBACA,UAAU;gBACZ;YACF;YAEA,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACnB,OAAO;oBACL,WAAW;wBACT,KAAK;oBACP;oBACA,QAAQ;gBACV;YACF;YAEA,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBACL,WAAW;wBACT,KAAK;oBACP;oBACA,WAAW;gBACb;YACF;YAEA,eAAe;YACf,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB,OAAO;oBAAE,QAAQ;gBAAY;gBAC7B,QAAQ;oBACN,IAAI;oBACJ,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,WAAW;gBACb;gBACA,SAAS;oBACP,WAAW;gBACb;gBACA,MAAM;YACR;YAEA,eAAe;YACf,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB,OAAO;oBAAE,WAAW;gBAAK;gBACzB,QAAQ;oBACN,IAAI;oBACJ,OAAO;oBACP,WAAW;oBACX,WAAW;oBACX,QAAQ;wBACN,QAAQ;4BACN,MAAM;wBACR;oBACF;gBACF;gBACA,SAAS;oBACP,WAAW;gBACb;gBACA,MAAM;YACR;YAEA,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;gBACV,SAAS;gBACT,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACnB,OAAO;wBACL,WAAW;4BACT,KAAK;wBACP;wBACA,UAAU;oBACZ;oBACA,QAAQ;wBACN,MAAM;wBACN,WAAW;oBACb;oBACA,SAAS;wBACP,WAAW;oBACb;oBACA,MAAM;gBACR,GAAG,IAAI,CAAC,CAAA,QAAS,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;4BAClC,MAAM;4BACN,aAAa,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC;4BAClC,WAAW,KAAK,SAAS,CAAC,WAAW;wBACvC,CAAC;gBAED,SAAS;gBACT,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;oBACtB,OAAO;wBACL,WAAW;4BACT,KAAK;wBACP;wBACA,QAAQ;oBACV;oBACA,QAAQ;wBACN,OAAO;wBACP,WAAW;wBACX,QAAQ;4BACN,QAAQ;gCACN,MAAM;4BACR;wBACF;oBACF;oBACA,SAAS;wBACP,WAAW;oBACb;oBACA,MAAM;gBACR,GAAG,IAAI,CAAC,CAAA,WAAY,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;4BAC3C,MAAM;4BACN,aAAa,GAAG,QAAQ,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;4BAC9D,WAAW,QAAQ,SAAS,CAAC,WAAW;wBAC1C,CAAC;gBAED,SAAS;gBACT,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACnB,OAAO;wBACL,WAAW;4BACT,KAAK;wBACP;wBACA,WAAW;oBACb;oBACA,QAAQ;wBACN,OAAO;wBACP,WAAW;wBACX,QAAQ;4BACN,QAAQ;gCACN,MAAM;4BACR;wBACF;oBACF;oBACA,SAAS;wBACP,WAAW;oBACb;oBACA,MAAM;gBACR,GAAG,IAAI,CAAC,CAAA,QAAS,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;4BAClC,MAAM;4BACN,aAAa,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC;4BACxD,WAAW,KAAK,SAAS,CAAC,WAAW;wBACvC,CAAC;aACF,EAAE,IAAI,CAAC,CAAA;gBACN,eAAe;gBACf,MAAM,gBAAgB,WAAW,IAAI;gBACrC,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAC5B,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC/D,KAAK,CAAC,GAAG;YACb;SACD;QAED,QAAQ;QACR,MAAM,sBAAsB,CAAC,SAAiB;YAC5C,IAAI,YAAY,GAAG,OAAO;YAC1B,MAAM,WAAW,UAAU;YAC3B,IAAI,aAAa,GAAG,OAAO,SAAS,IAAI,MAAM;YAC9C,OAAO,KAAK,KAAK,CAAC,AAAC,SAAS,WAAY;QAC1C;QAEA,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;YACA;YACA,YAAY,oBAAoB,YAAY;YAC5C,eAAe,oBAAoB,eAAe;YAClD,YAAY,oBAAoB,YAAY;YAC5C;YACA;YACA;YACA;YACA,UAAU;YACV,OAAO;gBACL,iBAAiB,gBAAgB,IAAI,KAAK,KAAK,CAAC,aAAa,iBAAiB;gBAC9E,kBAAkB;gBAClB,uBAAuB,gBAAgB;YACzC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
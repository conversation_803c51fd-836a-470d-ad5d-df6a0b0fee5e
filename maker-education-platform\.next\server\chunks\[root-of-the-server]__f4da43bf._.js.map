{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar ?? undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session, context)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session, context)\n    } catch (error) {\n      console.error('Admin auth error:', error)\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/admin/dashboard/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { withAdmin } from '@/lib/auth-utils'\n\nexport const GET = withAdmin(async (request: NextRequest, session: any) => {\n  try {\n    // 获取统计数据\n    const [\n      totalUsers,\n      totalProjects,\n      pendingApplications,\n      totalTeamMembers,\n      totalPosts,\n      publishedPosts,\n      recentProjects,\n      recentApplications,\n      recentPosts,\n      todayStats\n    ] = await Promise.all([\n      // 总用户数\n      prisma.user.count({\n        where: { isActive: true }\n      }),\n      \n      // 总作品数\n      prisma.project.count({\n        where: { status: 'PUBLISHED' }\n      }),\n      \n      // 待审核申请数\n      prisma.application.count({\n        where: { status: 'PENDING' }\n      }),\n      \n      // 团队成员数\n      prisma.teamMember.count({\n        where: { isActive: true }\n      }),\n\n      // 总文章数\n      prisma.post.count(),\n\n      // 已发布文章数\n      prisma.post.count({\n        where: { published: true }\n      }),\n      \n      // 最新作品\n      prisma.project.findMany({\n        where: { status: 'PUBLISHED' },\n        include: {\n          author: {\n            select: {\n              id: true,\n              name: true,\n              avatar: true,\n            }\n          }\n        },\n        orderBy: { createdAt: 'desc' },\n        take: 10\n      }),\n      \n      // 最新申请\n      prisma.application.findMany({\n        include: {\n          applicant: {\n            select: {\n              id: true,\n              name: true,\n              avatar: true,\n            }\n          }\n        },\n        orderBy: { createdAt: 'desc' },\n        take: 10\n      }),\n\n      // 最新文章\n      prisma.post.findMany({\n        include: {\n          author: {\n            select: {\n              id: true,\n              name: true,\n              avatar: true,\n            }\n          }\n        },\n        orderBy: { createdAt: 'desc' },\n        take: 5\n      }),\n\n      // 今日统计\n      Promise.all([\n        // 今日新用户\n        prisma.user.count({\n          where: {\n            createdAt: {\n              gte: new Date(new Date().setHours(0, 0, 0, 0))\n            }\n          }\n        }),\n        // 今日新项目\n        prisma.project.count({\n          where: {\n            createdAt: {\n              gte: new Date(new Date().setHours(0, 0, 0, 0))\n            }\n          }\n        }),\n        // 今日新申请\n        prisma.application.count({\n          where: {\n            createdAt: {\n              gte: new Date(new Date().setHours(0, 0, 0, 0))\n            }\n          }\n        })\n      ]).then(([newUsers, newProjects, newApplications]) => ({\n        newUsers,\n        newProjects,\n        newApplications\n      }))\n    ])\n\n    const dashboardStats = {\n      totalUsers,\n      totalProjects,\n      pendingApplications,\n      totalTeamMembers,\n      totalPosts,\n      publishedPosts,\n      recentProjects,\n      recentApplications,\n      recentPosts,\n      todayStats,\n      // 计算增长率（简化版本）\n      growthRates: {\n        users: todayStats.newUsers,\n        projects: todayStats.newProjects,\n        applications: todayStats.newApplications\n      }\n    }\n\n    return NextResponse.json(dashboardStats)\n  } catch (error) {\n    console.error('Failed to fetch dashboard stats:', error)\n    return NextResponse.json(\n      { error: '获取仪表板数据失败' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAsB;IACxD,IAAI;QACF,SAAS;QACT,MAAM,CACJ,YACA,eACA,qBACA,kBACA,YACA,gBACA,gBACA,oBACA,aACA,WACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,UAAU;gBAAK;YAC1B;YAEA,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACnB,OAAO;oBAAE,QAAQ;gBAAY;YAC/B;YAEA,SAAS;YACT,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBACvB,OAAO;oBAAE,QAAQ;gBAAU;YAC7B;YAEA,QAAQ;YACR,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBACtB,OAAO;oBAAE,UAAU;gBAAK;YAC1B;YAEA,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;YAEjB,SAAS;YACT,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,WAAW;gBAAK;YAC3B;YAEA,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB,OAAO;oBAAE,QAAQ;gBAAY;gBAC7B,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,IAAI;4BACJ,MAAM;4BACN,QAAQ;wBACV;oBACF;gBACF;gBACA,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,MAAM;YACR;YAEA,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC1B,SAAS;oBACP,WAAW;wBACT,QAAQ;4BACN,IAAI;4BACJ,MAAM;4BACN,QAAQ;wBACV;oBACF;gBACF;gBACA,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,MAAM;YACR;YAEA,OAAO;YACP,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,IAAI;4BACJ,MAAM;4BACN,QAAQ;wBACV;oBACF;gBACF;gBACA,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,MAAM;YACR;YAEA,OAAO;YACP,QAAQ,GAAG,CAAC;gBACV,QAAQ;gBACR,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;oBAChB,OAAO;wBACL,WAAW;4BACT,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,GAAG,GAAG;wBAC7C;oBACF;gBACF;gBACA,QAAQ;gBACR,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;oBACnB,OAAO;wBACL,WAAW;4BACT,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,GAAG,GAAG;wBAC7C;oBACF;gBACF;gBACA,QAAQ;gBACR,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,CAAC;oBACvB,OAAO;wBACL,WAAW;4BACT,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,GAAG,GAAG;wBAC7C;oBACF;gBACF;aACD,EAAE,IAAI,CAAC,CAAC,CAAC,UAAU,aAAa,gBAAgB,GAAK,CAAC;oBACrD;oBACA;oBACA;gBACF,CAAC;SACF;QAED,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,cAAc;YACd,aAAa;gBACX,OAAO,WAAW,QAAQ;gBAC1B,UAAU,WAAW,WAAW;gBAChC,cAAc,WAAW,eAAe;YAC1C;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAY,GACrB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
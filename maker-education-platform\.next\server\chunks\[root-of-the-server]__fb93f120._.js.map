{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/posts/slug/%5Bslug%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ slug: string }> }\n) {\n  try {\n    const { slug } = await params\n\n    const post = await prisma.post.findUnique({\n      where: {\n        slug,\n        published: true\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    if (!post) {\n      return NextResponse.json(\n        { error: '文章未找到' },\n        { status: 404 }\n      )\n    }\n\n    // 增加浏览次数\n    await prisma.post.update({\n      where: { id: post.id },\n      data: {\n        viewCount: {\n          increment: 1\n        }\n      }\n    })\n\n    // 解析JSON字符串为数组\n    const processedPost = {\n      ...post,\n      tags: post.tags ? JSON.parse(post.tags) : []\n    }\n\n    return NextResponse.json(processedPost)\n  } catch (error) {\n    console.error('Failed to fetch post:', error)\n    return NextResponse.json(\n      { error: '获取文章失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAyC;IAEjD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;QAEvB,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBACL;gBACA,WAAW;YACb;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC;YACrB,MAAM;gBACJ,WAAW;oBACT,WAAW;gBACb;YACF;QACF;QAEA,eAAe;QACf,MAAM,gBAAgB;YACpB,GAAG,IAAI;YACP,MAAM,KAAK,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;QAC9C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          username: user.username,\n          role: user.role,\n          avatar: user.avatar ?? undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth'\nimport { authOptions } from './auth'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function getSession() {\n  return await getServerSession(authOptions)\n}\n\nexport async function requireAuth() {\n  const session = await getSession()\n  if (!session) {\n    throw new Error('未授权访问')\n  }\n  return session\n}\n\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('需要管理员权限')\n  }\n  return session\n}\n\nexport function withAuth(handler: Function) {\n  return async (request: NextRequest) => {\n    try {\n      const session = await requireAuth()\n      return await handler(request, session)\n    } catch (error) {\n      return NextResponse.json(\n        { error: '未授权访问' },\n        { status: 401 }\n      )\n    }\n  }\n}\n\nexport function withAdmin(handler: Function) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const session = await requireAdmin()\n      return await handler(request, session, context)\n    } catch (error) {\n      console.error('Admin auth error:', error)\n      return NextResponse.json(\n        { error: '需要管理员权限' },\n        { status: 403 }\n      )\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;AAC3C;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,SAAS,OAAiB;IACxC,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAEO,SAAS,UAAU,OAAiB;IACzC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,OAAO,MAAM,QAAQ,SAAS,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/api/admin/system/status/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { withAdmin } from '@/lib/auth-utils'\nimport os from 'os'\n\nexport const GET = withAdmin(async (request: NextRequest, session: any) => {\n  try {\n    const startTime = Date.now()\n    \n    // 测试数据库连接\n    let dbStatus = 'healthy'\n    let dbResponseTime = 0\n    try {\n      const dbStart = Date.now()\n      await prisma.$queryRaw`SELECT 1`\n      dbResponseTime = Date.now() - dbStart\n    } catch (error) {\n      dbStatus = 'error'\n      dbResponseTime = -1\n    }\n\n    // 获取系统信息\n    const systemInfo = {\n      platform: os.platform(),\n      arch: os.arch(),\n      nodeVersion: process.version,\n      uptime: process.uptime(),\n      memory: {\n        total: os.totalmem(),\n        free: os.freemem(),\n        used: os.totalmem() - os.freemem(),\n        usage: ((os.totalmem() - os.freemem()) / os.totalmem() * 100).toFixed(2)\n      },\n      cpu: {\n        cores: os.cpus().length,\n        model: os.cpus()[0]?.model || 'Unknown',\n        load: os.loadavg()\n      }\n    }\n\n    // 获取数据库统计\n    const [\n      totalUsers,\n      totalProjects,\n      totalPosts,\n      totalApplications,\n      dbSize\n    ] = await Promise.all([\n      prisma.user.count(),\n      prisma.project.count(),\n      prisma.post.count(),\n      prisma.application.count(),\n      // 估算数据库大小（SQLite特定）\n      prisma.$queryRaw`SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()`.then(\n        (result: any) => Number(result[0]?.size || 0)\n      ).catch(() => 0)\n    ])\n\n    // 检查关键服务状态\n    const services = [\n      {\n        name: 'Database',\n        status: dbStatus,\n        responseTime: dbResponseTime,\n        details: `${totalUsers + totalProjects + totalPosts + totalApplications} records`\n      },\n      {\n        name: 'Authentication',\n        status: 'healthy',\n        responseTime: 5,\n        details: 'NextAuth.js running'\n      },\n      {\n        name: 'File System',\n        status: 'healthy',\n        responseTime: 2,\n        details: 'Read/Write access available'\n      }\n    ]\n\n    // 计算总体健康状态\n    const overallStatus = services.every(s => s.status === 'healthy') ? 'healthy' : 'warning'\n    \n    // API响应时间\n    const apiResponseTime = Date.now() - startTime\n\n    const systemStatus = {\n      status: overallStatus,\n      timestamp: new Date().toISOString(),\n      uptime: systemInfo.uptime,\n      version: '1.0.0',\n      environment: process.env.NODE_ENV || 'development',\n      \n      // 性能指标\n      performance: {\n        apiResponseTime,\n        dbResponseTime,\n        memoryUsage: systemInfo.memory.usage,\n        cpuCores: systemInfo.cpu.cores,\n        loadAverage: systemInfo.cpu.load[0]\n      },\n      \n      // 系统资源\n      resources: {\n        memory: {\n          total: Math.round(systemInfo.memory.total / 1024 / 1024 / 1024 * 100) / 100, // GB\n          used: Math.round(systemInfo.memory.used / 1024 / 1024 / 1024 * 100) / 100, // GB\n          free: Math.round(systemInfo.memory.free / 1024 / 1024 / 1024 * 100) / 100, // GB\n          usage: parseFloat(systemInfo.memory.usage)\n        },\n        cpu: {\n          cores: systemInfo.cpu.cores,\n          model: systemInfo.cpu.model,\n          load: systemInfo.cpu.load\n        },\n        disk: {\n          database: Math.round(Number(dbSize) / 1024 / 1024 * 100) / 100 // MB\n        }\n      },\n      \n      // 服务状态\n      services,\n      \n      // 数据库统计\n      database: {\n        status: dbStatus,\n        responseTime: dbResponseTime,\n        records: {\n          users: totalUsers,\n          projects: totalProjects,\n          posts: totalPosts,\n          applications: totalApplications,\n          total: totalUsers + totalProjects + totalPosts + totalApplications\n        },\n        size: Math.round(dbSize / 1024 / 1024 * 100) / 100 // MB\n      },\n      \n      // 系统信息\n      system: {\n        platform: systemInfo.platform,\n        architecture: systemInfo.arch,\n        nodeVersion: systemInfo.nodeVersion,\n        uptime: Math.round(systemInfo.uptime),\n        startTime: new Date(Date.now() - systemInfo.uptime * 1000).toISOString()\n      }\n    }\n\n    return NextResponse.json(systemStatus)\n  } catch (error) {\n    console.error('Failed to get system status:', error)\n    return NextResponse.json(\n      { \n        status: 'error',\n        error: '获取系统状态失败',\n        timestamp: new Date().toISOString()\n      },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAsB;IACxD,IAAI;QACF,MAAM,YAAY,KAAK,GAAG;QAE1B,UAAU;QACV,IAAI,WAAW;QACf,IAAI,iBAAiB;QACrB,IAAI;YACF,MAAM,UAAU,KAAK,GAAG;YACxB,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YAChC,iBAAiB,KAAK,GAAG,KAAK;QAChC,EAAE,OAAO,OAAO;YACd,WAAW;YACX,iBAAiB,CAAC;QACpB;QAEA,SAAS;QACT,MAAM,aAAa;YACjB,UAAU,6FAAA,CAAA,UAAE,CAAC,QAAQ;YACrB,MAAM,6FAAA,CAAA,UAAE,CAAC,IAAI;YACb,aAAa,QAAQ,OAAO;YAC5B,QAAQ,QAAQ,MAAM;YACtB,QAAQ;gBACN,OAAO,6FAAA,CAAA,UAAE,CAAC,QAAQ;gBAClB,MAAM,6FAAA,CAAA,UAAE,CAAC,OAAO;gBAChB,MAAM,6FAAA,CAAA,UAAE,CAAC,QAAQ,KAAK,6FAAA,CAAA,UAAE,CAAC,OAAO;gBAChC,OAAO,CAAC,CAAC,6FAAA,CAAA,UAAE,CAAC,QAAQ,KAAK,6FAAA,CAAA,UAAE,CAAC,OAAO,EAAE,IAAI,6FAAA,CAAA,UAAE,CAAC,QAAQ,KAAK,GAAG,EAAE,OAAO,CAAC;YACxE;YACA,KAAK;gBACH,OAAO,6FAAA,CAAA,UAAE,CAAC,IAAI,GAAG,MAAM;gBACvB,OAAO,6FAAA,CAAA,UAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,SAAS;gBAC9B,MAAM,6FAAA,CAAA,UAAE,CAAC,OAAO;YAClB;QACF;QAEA,UAAU;QACV,MAAM,CACJ,YACA,eACA,YACA,mBACA,OACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;YACjB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK;YACpB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;YACjB,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK;YACxB,oBAAoB;YACpB,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,kFAAkF,CAAC,CAAC,IAAI,CACvG,CAAC,SAAgB,OAAO,MAAM,CAAC,EAAE,EAAE,QAAQ,IAC3C,KAAK,CAAC,IAAM;SACf;QAED,WAAW;QACX,MAAM,WAAW;YACf;gBACE,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,SAAS,GAAG,aAAa,gBAAgB,aAAa,kBAAkB,QAAQ,CAAC;YACnF;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,SAAS;YACX;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,SAAS;YACX;SACD;QAED,WAAW;QACX,MAAM,gBAAgB,SAAS,KAAK,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,YAAY;QAEhF,UAAU;QACV,MAAM,kBAAkB,KAAK,GAAG,KAAK;QAErC,MAAM,eAAe;YACnB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ,WAAW,MAAM;YACzB,SAAS;YACT,aAAa,mDAAwB;YAErC,OAAO;YACP,aAAa;gBACX;gBACA;gBACA,aAAa,WAAW,MAAM,CAAC,KAAK;gBACpC,UAAU,WAAW,GAAG,CAAC,KAAK;gBAC9B,aAAa,WAAW,GAAG,CAAC,IAAI,CAAC,EAAE;YACrC;YAEA,OAAO;YACP,WAAW;gBACT,QAAQ;oBACN,OAAO,KAAK,KAAK,CAAC,WAAW,MAAM,CAAC,KAAK,GAAG,OAAO,OAAO,OAAO,OAAO;oBACxE,MAAM,KAAK,KAAK,CAAC,WAAW,MAAM,CAAC,IAAI,GAAG,OAAO,OAAO,OAAO,OAAO;oBACtE,MAAM,KAAK,KAAK,CAAC,WAAW,MAAM,CAAC,IAAI,GAAG,OAAO,OAAO,OAAO,OAAO;oBACtE,OAAO,WAAW,WAAW,MAAM,CAAC,KAAK;gBAC3C;gBACA,KAAK;oBACH,OAAO,WAAW,GAAG,CAAC,KAAK;oBAC3B,OAAO,WAAW,GAAG,CAAC,KAAK;oBAC3B,MAAM,WAAW,GAAG,CAAC,IAAI;gBAC3B;gBACA,MAAM;oBACJ,UAAU,KAAK,KAAK,CAAC,OAAO,UAAU,OAAO,OAAO,OAAO,IAAI,KAAK;gBACtE;YACF;YAEA,OAAO;YACP;YAEA,QAAQ;YACR,UAAU;gBACR,QAAQ;gBACR,cAAc;gBACd,SAAS;oBACP,OAAO;oBACP,UAAU;oBACV,OAAO;oBACP,cAAc;oBACd,OAAO,aAAa,gBAAgB,aAAa;gBACnD;gBACA,MAAM,KAAK,KAAK,CAAC,SAAS,OAAO,OAAO,OAAO,IAAI,KAAK;YAC1D;YAEA,OAAO;YACP,QAAQ;gBACN,UAAU,WAAW,QAAQ;gBAC7B,cAAc,WAAW,IAAI;gBAC7B,aAAa,WAAW,WAAW;gBACnC,QAAQ,KAAK,KAAK,CAAC,WAAW,MAAM;gBACpC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,MAAM,GAAG,MAAM,WAAW;YACxE;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;YACR,OAAO;YACP,WAAW,IAAI,OAAO,WAAW;QACnC,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
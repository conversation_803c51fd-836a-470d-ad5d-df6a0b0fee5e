{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string) {\n  return new Date(date).toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport function formatDateTime(date: Date | string) {\n  return new Date(date).toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  })\n}\n\nexport function truncateText(text: string, maxLength: number) {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,MAAM,cAAc,CAAC,SAAS;QAC5C,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { ArrowRight, Users, Lightbulb, Award, BookOpen } from 'lucide-react'\n\n\nasync function getSettings() {\n  try {\n    // 使用公共设置API\n    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/settings/public`, {\n      cache: 'no-store'\n    })\n\n    if (response.ok) {\n      const settingsData = await response.json()\n      // 公共设置API直接返回键值对\n      const settingsMap = settingsData\n\n      return {\n        siteName: settingsMap.site_name || '创客教育平台',\n        siteDescription: settingsMap.site_description || '激发创造力，培养未来创新人才',\n        heroTitle: settingsMap.hero_title || '创新思维 · 实践能力',\n        heroSubtitle: settingsMap.hero_subtitle || 'STEAM创客教育平台',\n        heroDescription: settingsMap.hero_description || '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',\n        contactEmail: settingsMap.contact_email || '<EMAIL>',\n        contactPhone: settingsMap.contact_phone || '************',\n        contactAddress: settingsMap.contact_address || '北京市朝阳区创新大厦',\n        teamCount: parseInt(settingsMap.team_count || '50'),\n        enableRegistration: settingsMap.allow_registration === 'true',\n        statsProjects: parseInt(settingsMap.stats_projects || '500'),\n        statsStudents: parseInt(settingsMap.stats_students || '1200'),\n        statsSatisfaction: parseInt(settingsMap.stats_satisfaction || '98')\n      }\n    }\n  } catch (error) {\n    console.error('Failed to fetch settings:', error)\n  }\n\n  // 返回默认值\n  return {\n    siteName: '创客教育平台',\n    siteDescription: '激发创造力，培养未来创新人才',\n    heroTitle: '创新思维 · 实践能力',\n    heroSubtitle: 'STEAM创客教育平台',\n    heroDescription: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',\n    contactEmail: '<EMAIL>',\n    contactPhone: '************',\n    contactAddress: '北京市朝阳区创新大厦',\n    teamCount: 50,\n    enableRegistration: true,\n    statsProjects: 500,\n    statsStudents: 1200,\n    statsSatisfaction: 98\n  }\n}\n\nexport default async function Home() {\n  const settings = await getSettings()\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-20 overflow-hidden\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-float\"></div>\n        <div className=\"absolute bottom-10 right-10 w-24 h-24 bg-indigo-200 rounded-full opacity-20 animate-float-delayed\"></div>\n        <div className=\"absolute top-1/2 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-float-slow\"></div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\n          <div className=\"text-center animate-fade-in\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              {settings.heroTitle}\n              <span className=\"block text-blue-600\">{settings.heroSubtitle}</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              {settings.heroDescription}\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center animate-slide-up\">\n              <Link href=\"/projects\">\n                <Button size=\"lg\" className=\"w-full sm:w-auto btn-enhanced rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-enhanced\">\n                  探索作品展示\n                  <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n                </Button>\n              </Link>\n              <Link href=\"/apply\">\n                <Button variant=\"outline\" size=\"lg\" className=\"w-full sm:w-auto btn-enhanced rounded-xl border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white\">\n                  申请加入团队\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              为什么选择我们的创客教育\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              我们提供全方位的STEAM教育解决方案，让学习变得更有趣、更有效\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <div className=\"text-center p-6 rounded-xl border border-gray-200 card-hover-enhanced bg-white shadow-enhanced\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Lightbulb className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">创新思维</h3>\n              <p className=\"text-gray-600\">\n                培养学生的创新思维和问题解决能力，激发无限创造潜能\n              </p>\n            </div>\n\n            <div className=\"text-center p-6 rounded-xl border border-gray-200 card-hover-enhanced bg-white shadow-enhanced\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <BookOpen className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">实践教学</h3>\n              <p className=\"text-gray-600\">\n                通过动手实践，让学生在制作过程中掌握科学技术知识\n              </p>\n            </div>\n\n            <div className=\"text-center p-6 rounded-xl border border-gray-200 card-hover-enhanced bg-white shadow-enhanced\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Users className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">团队协作</h3>\n              <p className=\"text-gray-600\">\n                培养团队合作精神，提升沟通协调和项目管理能力\n              </p>\n            </div>\n\n            <div className=\"text-center p-6 rounded-xl border border-gray-200 card-hover-enhanced bg-white shadow-enhanced\">\n              <div className=\"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Award className=\"h-8 w-8 text-orange-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">成果展示</h3>\n              <p className=\"text-gray-600\">\n                展示学生优秀作品，建立自信心和成就感\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">{settings.total_members || 50}+</div>\n              <div className=\"text-gray-600\">总成员数</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-green-600 mb-2\">{settings.total_projects || 120}+</div>\n              <div className=\"text-gray-600\">总项目数</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-purple-600 mb-2\">{settings.active_members || 35}+</div>\n              <div className=\"text-gray-600\">活跃成员</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-orange-600 mb-2\">{settings.completed_projects || 95}+</div>\n              <div className=\"text-gray-600\">完成项目</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative py-20 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 overflow-hidden\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-black opacity-10\"></div>\n        <div className=\"absolute top-10 right-10 w-40 h-40 bg-white rounded-full opacity-10 animate-float\"></div>\n        <div className=\"absolute bottom-10 left-10 w-32 h-32 bg-white rounded-full opacity-10 animate-float-delayed\"></div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4 animate-fade-in\">\n            准备好开始你的创客之旅了吗？\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto animate-slide-up\">\n            加入我们的创客教育平台，与志同道合的伙伴一起探索科技的无限可能\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center animate-slide-up\">\n            <Link href=\"/apply\">\n              <Button size=\"lg\" className=\"w-full sm:w-auto btn-enhanced rounded-xl bg-white text-blue-600 hover:bg-gray-100 shadow-enhanced\">\n                立即申请加入\n              </Button>\n            </Link>\n            <Link href=\"/about\">\n              <Button size=\"lg\" variant=\"outline\" className=\"w-full sm:w-auto btn-enhanced rounded-xl border-2 border-white text-white hover:bg-white hover:text-blue-600\">\n                了解更多\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAGA,eAAe;IACb,IAAI;QACF,YAAY;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,oBAAoB,CAAC,EAAE;YACzG,OAAO;QACT;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,eAAe,MAAM,SAAS,IAAI;YACxC,iBAAiB;YACjB,MAAM,cAAc;YAEpB,OAAO;gBACL,UAAU,YAAY,SAAS,IAAI;gBACnC,iBAAiB,YAAY,gBAAgB,IAAI;gBACjD,WAAW,YAAY,UAAU,IAAI;gBACrC,cAAc,YAAY,aAAa,IAAI;gBAC3C,iBAAiB,YAAY,gBAAgB,IAAI;gBACjD,cAAc,YAAY,aAAa,IAAI;gBAC3C,cAAc,YAAY,aAAa,IAAI;gBAC3C,gBAAgB,YAAY,eAAe,IAAI;gBAC/C,WAAW,SAAS,YAAY,UAAU,IAAI;gBAC9C,oBAAoB,YAAY,kBAAkB,KAAK;gBACvD,eAAe,SAAS,YAAY,cAAc,IAAI;gBACtD,eAAe,SAAS,YAAY,cAAc,IAAI;gBACtD,mBAAmB,SAAS,YAAY,kBAAkB,IAAI;YAChE;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,QAAQ;IACR,OAAO;QACL,UAAU;QACV,iBAAiB;QACjB,WAAW;QACX,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,WAAW;QACX,oBAAoB;QACpB,eAAe;QACf,eAAe;QACf,mBAAmB;IACrB;AACF;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCACX,SAAS,SAAS;sDACnB,8OAAC;4CAAK,WAAU;sDAAuB,SAAS,YAAY;;;;;;;;;;;;8CAE9D,8OAAC;oCAAE,WAAU;8CACV,SAAS,eAAe;;;;;;8CAE3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;oDAAgJ;kEAE1K,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG1B,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAqH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7K,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;4CAAyC,SAAS,aAAa,IAAI;4CAAG;;;;;;;kDACrF,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;4CAA0C,SAAS,cAAc,IAAI;4CAAI;;;;;;;kDACxF,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;4CAA2C,SAAS,cAAc,IAAI;4CAAG;;;;;;;kDACxF,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;4CAA2C,SAAS,kBAAkB,IAAI;4CAAG;;;;;;;kDAC5F,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiE;;;;;;0CAG/E,8OAAC;gCAAE,WAAU;0CAAgE;;;;;;0CAG7E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAoG;;;;;;;;;;;kDAIlI,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,WAAU;sDAA+G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3K", "debugId": null}}]}
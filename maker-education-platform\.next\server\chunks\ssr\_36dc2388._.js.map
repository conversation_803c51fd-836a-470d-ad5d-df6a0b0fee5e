{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/privacy/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\nexport const metadata: Metadata = {\n  title: '隐私政策 - 创客教育平台',\n  description: '了解我们如何收集、使用和保护您的个人信息',\n}\n\nexport default function PrivacyPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-xl shadow-lg p-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">隐私政策</h1>\n          \n          <div className=\"prose prose-lg max-w-none\">\n            <p className=\"text-gray-600 mb-6\">\n              最后更新时间：2024年1月1日\n            </p>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">1. 信息收集</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们可能收集以下类型的信息：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>个人身份信息：姓名、电子邮件地址、电话号码</li>\n                <li>账户信息：用户名、密码（加密存储）</li>\n                <li>教育信息：学校、年级、学习兴趣</li>\n                <li>使用数据：访问时间、页面浏览、功能使用情况</li>\n                <li>设备信息：IP地址、浏览器类型、操作系统</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">2. 信息使用</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们使用收集的信息用于：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>提供和改进我们的教育服务</li>\n                <li>处理用户注册和申请</li>\n                <li>发送重要通知和更新</li>\n                <li>分析平台使用情况以优化用户体验</li>\n                <li>防止欺诈和确保平台安全</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">3. 信息共享</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们不会出售、交易或转让您的个人信息给第三方，除非：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>获得您的明确同意</li>\n                <li>法律要求或政府部门要求</li>\n                <li>保护我们的权利、财产或安全</li>\n                <li>与可信的第三方服务提供商合作（如云存储服务）</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">4. 数据安全</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们采取多种安全措施保护您的个人信息：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>使用SSL加密传输敏感数据</li>\n                <li>定期更新安全协议和系统</li>\n                <li>限制员工访问个人信息</li>\n                <li>定期备份数据以防丢失</li>\n                <li>监控异常访问活动</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">5. Cookie使用</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们使用Cookie和类似技术来：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>记住您的登录状态</li>\n                <li>分析网站流量和使用模式</li>\n                <li>个性化您的用户体验</li>\n                <li>提供相关的内容和功能</li>\n              </ul>\n              <p className=\"text-gray-700 mt-4\">\n                您可以通过浏览器设置控制Cookie的使用，但这可能影响某些功能的正常使用。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">6. 用户权利</h2>\n              <p className=\"text-gray-700 mb-4\">\n                您对自己的个人信息享有以下权利：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>访问权：查看我们持有的您的个人信息</li>\n                <li>更正权：要求更正不准确的信息</li>\n                <li>删除权：要求删除您的个人信息</li>\n                <li>限制处理权：限制我们处理您的信息</li>\n                <li>数据可携权：获取您的数据副本</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">7. 儿童隐私</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们特别重视儿童的隐私保护：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>13岁以下儿童需要家长或监护人同意才能使用我们的服务</li>\n                <li>我们不会故意收集13岁以下儿童的个人信息</li>\n                <li>如发现收集了儿童信息，我们将立即删除</li>\n                <li>家长可以随时要求查看、更正或删除其子女的信息</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">8. 政策更新</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们可能会不时更新本隐私政策。重大变更时，我们将：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>在网站上发布更新通知</li>\n                <li>通过电子邮件通知注册用户</li>\n                <li>在政策生效前提供合理的通知期</li>\n                <li>更新页面顶部的\"最后更新时间\"</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">9. 联系我们</h2>\n              <p className=\"text-gray-700 mb-4\">\n                如果您对本隐私政策有任何疑问或需要行使您的权利，请通过以下方式联系我们：\n              </p>\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <p className=\"text-gray-700\"><strong>邮箱：</strong> <EMAIL></p>\n                <p className=\"text-gray-700\"><strong>电话：</strong> 400-123-4567</p>\n                <p className=\"text-gray-700\"><strong>地址：</strong> 北京市朝阳区创新大厦</p>\n                <p className=\"text-gray-700\"><strong>工作时间：</strong> 周一至周五 9:00-18:00</p>\n              </div>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">10. 法律适用</h2>\n              <p className=\"text-gray-700\">\n                本隐私政策受中华人民共和国法律管辖。如发生争议，双方应友好协商解决；\n                协商不成的，应向我们所在地有管辖权的人民法院提起诉讼。\n              </p>\n            </section>\n\n            <div className=\"mt-12 p-6 bg-blue-50 rounded-lg border border-blue-200\">\n              <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">重要提醒</h3>\n              <p className=\"text-blue-800\">\n                请仔细阅读本隐私政策。继续使用我们的服务即表示您同意本政策的条款。\n                如果您不同意本政策，请停止使用我们的服务。\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAIlC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAEN,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAKpC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;kEAAgB,8OAAC;kEAAO;;;;;;oDAAY;;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;kEAAgB,8OAAC;kEAAO;;;;;;oDAAY;;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;kEAAgB,8OAAC;kEAAO;;;;;;oDAAY;;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;kEAAgB,8OAAC;kEAAO;;;;;;oDAAc;;;;;;;;;;;;;;;;;;;0CAIvD,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAM/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,IAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}
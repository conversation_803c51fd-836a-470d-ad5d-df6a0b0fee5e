{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/terms/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\nexport const metadata: Metadata = {\n  title: '使用条款 - 创客教育平台',\n  description: '了解使用我们平台的条款和条件',\n}\n\nexport default function TermsPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-xl shadow-lg p-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">使用条款</h1>\n          \n          <div className=\"prose prose-lg max-w-none\">\n            <p className=\"text-gray-600 mb-6\">\n              最后更新时间：2024年1月1日\n            </p>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">1. 接受条款</h2>\n              <p className=\"text-gray-700 mb-4\">\n                欢迎使用创客教育平台！通过访问或使用我们的服务，您同意受本使用条款的约束。\n                如果您不同意这些条款，请不要使用我们的服务。\n              </p>\n              <p className=\"text-gray-700\">\n                我们保留随时修改这些条款的权利。修改后的条款将在发布后立即生效。\n                继续使用服务即表示您接受修改后的条款。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">2. 服务描述</h2>\n              <p className=\"text-gray-700 mb-4\">\n                创客教育平台是一个专注于STEAM教育的在线平台，提供：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>创客教育课程和资源</li>\n                <li>学生作品展示平台</li>\n                <li>教育博客和文章</li>\n                <li>团队申请和管理功能</li>\n                <li>在线学习和交流社区</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">3. 用户账户</h2>\n              <p className=\"text-gray-700 mb-4\">\n                使用某些服务需要创建账户。您同意：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>提供准确、完整的注册信息</li>\n                <li>及时更新您的账户信息</li>\n                <li>保护您的账户密码安全</li>\n                <li>对您账户下的所有活动负责</li>\n                <li>立即通知我们任何未经授权的使用</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">4. 用户行为规范</h2>\n              <p className=\"text-gray-700 mb-4\">\n                使用我们的服务时，您不得：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>发布违法、有害、威胁、辱骂或诽谤性内容</li>\n                <li>侵犯他人的知识产权或隐私权</li>\n                <li>传播病毒、恶意软件或其他有害代码</li>\n                <li>进行垃圾邮件发送或其他形式的骚扰</li>\n                <li>尝试未经授权访问系统或其他用户账户</li>\n                <li>干扰或破坏服务的正常运行</li>\n                <li>使用自动化工具抓取网站内容</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">5. 内容和知识产权</h2>\n              <p className=\"text-gray-700 mb-4\">\n                <strong>我们的内容：</strong>\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2 mb-4\">\n                <li>平台上的所有内容（除用户生成内容外）均为我们所有</li>\n                <li>受版权、商标和其他知识产权法保护</li>\n                <li>未经许可不得复制、分发或修改</li>\n              </ul>\n              \n              <p className=\"text-gray-700 mb-4\">\n                <strong>用户内容：</strong>\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>您保留对自己创建内容的所有权</li>\n                <li>授予我们使用、展示、分发您内容的许可</li>\n                <li>确保您的内容不侵犯他人权利</li>\n                <li>我们有权删除违规内容</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">6. 隐私保护</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们重视您的隐私。我们的隐私政策详细说明了我们如何收集、使用和保护您的信息。\n                使用我们的服务即表示您同意我们的隐私政策。\n              </p>\n              <p className=\"text-gray-700\">\n                特别地，对于未成年用户，我们将严格遵守相关法律法规，保护未成年人的隐私和安全。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">7. 服务可用性</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们努力保持服务的可用性，但不保证：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>服务将不间断或无错误</li>\n                <li>所有功能在所有时间都可用</li>\n                <li>服务不会受到技术问题影响</li>\n              </ul>\n              <p className=\"text-gray-700 mt-4\">\n                我们保留随时修改、暂停或终止服务的权利，恕不另行通知。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">8. 免责声明</h2>\n              <p className=\"text-gray-700 mb-4\">\n                在法律允许的最大范围内：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>服务按\"现状\"提供，不提供任何明示或暗示的保证</li>\n                <li>我们不对服务的准确性、可靠性或完整性负责</li>\n                <li>我们不对因使用服务而产生的任何损失负责</li>\n                <li>我们不对第三方内容或链接负责</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">9. 责任限制</h2>\n              <p className=\"text-gray-700 mb-4\">\n                在任何情况下，我们对您的总责任不超过：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>您在过去12个月内支付给我们的费用总额</li>\n                <li>如果服务免费，则为人民币100元</li>\n              </ul>\n              <p className=\"text-gray-700 mt-4\">\n                我们不对间接、偶然、特殊或后果性损害负责。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">10. 终止</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们可能在以下情况下终止您的账户：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>违反本使用条款</li>\n                <li>从事非法或有害活动</li>\n                <li>长期不活跃</li>\n                <li>其他我们认为必要的情况</li>\n              </ul>\n              <p className=\"text-gray-700 mt-4\">\n                您也可以随时删除您的账户。账户终止后，您将失去访问服务的权利。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">11. 争议解决</h2>\n              <p className=\"text-gray-700 mb-4\">\n                如果发生争议：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>首先尝试通过友好协商解决</li>\n                <li>协商不成的，提交我们所在地有管辖权的人民法院</li>\n                <li>适用中华人民共和国法律</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">12. 联系信息</h2>\n              <p className=\"text-gray-700 mb-4\">\n                如果您对本使用条款有任何疑问，请联系我们：\n              </p>\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <p className=\"text-gray-700\"><strong>邮箱：</strong> <EMAIL></p>\n                <p className=\"text-gray-700\"><strong>电话：</strong> 400-123-4567</p>\n                <p className=\"text-gray-700\"><strong>地址：</strong> 北京市朝阳区创新大厦</p>\n                <p className=\"text-gray-700\"><strong>工作时间：</strong> 周一至周五 9:00-18:00</p>\n              </div>\n            </section>\n\n            <div className=\"mt-12 p-6 bg-amber-50 rounded-lg border border-amber-200\">\n              <h3 className=\"text-lg font-semibold text-amber-900 mb-2\">重要提醒</h3>\n              <p className=\"text-amber-800\">\n                请仔细阅读并理解本使用条款。如果您是未成年人，请在家长或监护人的指导下使用我们的服务。\n                继续使用服务即表示您同意遵守这些条款。\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAIlC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAIlC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAM/B,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDACX,cAAA,8OAAC;sDAAO;;;;;;;;;;;kDAEV,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAE,WAAU;kDACX,cAAA,8OAAC;sDAAO;;;;;;;;;;;kDAEV,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAIlC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAK/B,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAEN,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAKpC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAEN,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAKpC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAEN,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAKpC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;kEAAgB,8OAAC;kEAAO;;;;;;oDAAY;;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;kEAAgB,8OAAC;kEAAO;;;;;;oDAAY;;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;kEAAgB,8OAAC;kEAAO;;;;;;oDAAY;;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;kEAAgB,8OAAC;kEAAO;;;;;;oDAAc;;;;;;;;;;;;;;;;;;;0CAIvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5C", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}
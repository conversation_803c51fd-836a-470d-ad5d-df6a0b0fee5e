{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/apply/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Upload, FileText, User, Mail, Phone, Briefcase } from 'lucide-react'\n\nconst positions = [\n  '教育专家',\n  '技术导师',\n  '项目经理',\n  '课程设计师',\n  '3D打印专家',\n  '机器人教练',\n  '编程导师',\n  '创新顾问',\n  '其他'\n]\n\nconst skillOptions = [\n  'STEAM教育',\n  '3D打印',\n  '机器人制作',\n  '编程教学',\n  '电子制作',\n  '项目管理',\n  '课程设计',\n  '创新思维',\n  '团队协作',\n  '沟通表达'\n]\n\nexport default function Apply() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState(false)\n\n  const [formData, setFormData] = useState<Record<string, any>>({\n    name: '',\n    email: '',\n    phone: '',\n    position: '',\n    skills: [] as string[],\n    experience: '',\n    motivation: '',\n    resume: ''\n  })\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    })\n  }\n\n  const handleSkillToggle = (skill: string) => {\n    setFormData(prev => ({\n      ...prev,\n      skills: prev.skills.includes(skill)\n        ? prev.skills.filter((s: string) => s !== skill)\n        : [...prev.skills, skill]\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError('')\n\n    // 验证必填字段\n    if (!formData.name || !formData.email || !formData.position || !formData.motivation) {\n      setError('请填写所有必填字段')\n      setIsLoading(false)\n      return\n    }\n\n    if (formData.skills.length === 0) {\n      setError('请至少选择一项技能')\n      setIsLoading(false)\n      return\n    }\n\n    try {\n      const response = await fetch('/api/applications', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        setSuccess(true)\n      } else {\n        setError(data.error || '申请提交失败，请稍后重试')\n      }\n    } catch (error) {\n      setError('申请提交失败，请稍后重试')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"max-w-md w-full text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">请先登录</h2>\n          <p className=\"text-gray-600 mb-6\">您需要登录后才能申请加入团队</p>\n          <Link href=\"/auth/signin\">\n            <Button>前往登录</Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  if (success) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"max-w-md w-full text-center\">\n          <div className=\"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n            </svg>\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">申请提交成功！</h2>\n          <p className=\"text-gray-600 mb-6\">\n            我们已收到您的申请，将在3-5个工作日内进行审核并与您联系。\n          </p>\n          <div className=\"space-y-3\">\n            <Link href=\"/\">\n              <Button className=\"w-full\">返回首页</Button>\n            </Link>\n            <Link href=\"/team\">\n              <Button variant=\"outline\" className=\"w-full\">了解团队</Button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              申请加入我们的团队\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              成为创客教育的推动者，与我们一起培养下一代的创新人才\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Application Form */}\n      <section className=\"py-12\">\n        <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"bg-white rounded-lg shadow-md p-8\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Personal Information */}\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <User className=\"w-5 h-5 mr-2\" />\n                  个人信息\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      姓名 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      required\n                      className={`w-full px-3 py-2 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                        !formData.name && error ? 'border-red-500 bg-red-50' : 'border-gray-300'\n                      }`}\n                      value={formData.name}\n                      onChange={handleChange}\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      邮箱 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      required\n                      className={`w-full px-3 py-2 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                        !formData.email && error ? 'border-red-500 bg-red-50' : 'border-gray-300'\n                      }`}\n                      value={formData.email}\n                      onChange={handleChange}\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      电话\n                    </label>\n                    <input\n                      type=\"tel\"\n                      id=\"phone\"\n                      name=\"phone\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      value={formData.phone}\n                      onChange={handleChange}\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"position\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      申请职位 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      id=\"position\"\n                      name=\"position\"\n                      required\n                      className={`w-full px-3 py-2 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                        !formData.position && error ? 'border-red-500 bg-red-50' : 'border-gray-300'\n                      }`}\n                      value={formData.position}\n                      onChange={handleChange}\n                    >\n                      <option value=\"\">请选择职位</option>\n                      {positions.map((position) => (\n                        <option key={position} value={position}>\n                          {position}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              {/* Skills */}\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  技能专长 <span className=\"text-red-500\">*</span>\n                </h3>\n                <div className={`grid grid-cols-2 md:grid-cols-3 gap-3 p-4 border rounded-xl ${\n                  formData.skills.length === 0 && error ? 'border-red-500 bg-red-50' : 'border-gray-200'\n                }`}>\n                  {skillOptions.map((skill) => (\n                    <label key={skill} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        checked={formData.skills.includes(skill)}\n                        onChange={() => handleSkillToggle(skill)}\n                      />\n                      <span className=\"ml-2 text-sm text-gray-700\">{skill}</span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* Experience */}\n              <div>\n                <label htmlFor=\"experience\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  相关经验\n                </label>\n                <textarea\n                  id=\"experience\"\n                  name=\"experience\"\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"请描述您在教育、技术或相关领域的经验...\"\n                  value={formData.experience}\n                  onChange={handleChange}\n                />\n              </div>\n\n              {/* Motivation */}\n              <div>\n                <label htmlFor=\"motivation\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  申请动机 <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"motivation\"\n                  name=\"motivation\"\n                  rows={4}\n                  required\n                  className={`w-full px-3 py-2 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    !formData.motivation && error ? 'border-red-500 bg-red-50' : 'border-gray-300'\n                  }`}\n                  placeholder=\"请告诉我们您为什么想要加入我们的团队，以及您能为团队带来什么...\"\n                  value={formData.motivation}\n                  onChange={handleChange}\n                />\n              </div>\n\n              {/* Resume Upload */}\n              <div>\n                <label htmlFor=\"resume\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  简历链接\n                </label>\n                <input\n                  type=\"url\"\n                  id=\"resume\"\n                  name=\"resume\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"请提供您的简历链接（如LinkedIn、个人网站等）\"\n                  value={formData.resume}\n                  onChange={handleChange}\n                />\n              </div>\n\n              {error && (\n                <div className=\"text-red-600 text-sm text-center bg-red-50 p-3 rounded-md\">\n                  {error}\n                </div>\n              )}\n\n              <div className=\"flex justify-end space-x-4\">\n                <Link href=\"/team\">\n                  <Button type=\"button\" variant=\"outline\" className=\"btn-enhanced rounded-xl\">\n                    了解团队\n                  </Button>\n                </Link>\n                <Button\n                  type=\"submit\"\n                  disabled={isLoading}\n                  className={`btn-enhanced btn-submit rounded-xl px-8 py-3 text-lg font-semibold transition-all duration-300 ${\n                    isLoading\n                      ? 'bg-gray-400 cursor-not-allowed animate-pulse'\n                      : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-enhanced hover:shadow-hover'\n                  }`}\n                >\n                  {isLoading ? (\n                    <div className=\"flex items-center\">\n                      <div className=\"loading-spinner w-4 h-4 mr-2\"></div>\n                      提交中...\n                    </div>\n                  ) : (\n                    '提交申请'\n                  )}\n                </Button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QAC5D,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ,EAAE;QACV,YAAY;QACZ,YAAY;QACZ,QAAQ;IACV;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,SACzB,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM,SACxC;uBAAI,KAAK,MAAM;oBAAE;iBAAM;YAC7B,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,SAAS;QACT,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,UAAU,EAAE;YACnF,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI,SAAS,MAAM,CAAC,MAAM,KAAK,GAAG;YAChC,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;YACb,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;sCAAC;;;;;;;;;;;;;;;;;;;;;;IAKlB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAyB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAChF,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAS;;;;;;;;;;;0CAE7B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;0BAQlE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;;gEAA+C;8EAC1E,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAEpC,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,WAAW,CAAC,6FAA6F,EACvG,CAAC,SAAS,IAAI,IAAI,QAAQ,6BAA6B,mBACvD;4DACF,OAAO,SAAS,IAAI;4DACpB,UAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;;gEAA+C;8EAC3E,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAEpC,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,WAAW,CAAC,6FAA6F,EACvG,CAAC,SAAS,KAAK,IAAI,QAAQ,6BAA6B,mBACxD;4DACF,OAAO,SAAS,KAAK;4DACrB,UAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,OAAO,SAAS,KAAK;4DACrB,UAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;;gEAA+C;8EAC5E,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAEtC,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,WAAW,CAAC,6FAA6F,EACvG,CAAC,SAAS,QAAQ,IAAI,QAAQ,6BAA6B,mBAC3D;4DACF,OAAO,SAAS,QAAQ;4DACxB,UAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wEAAsB,OAAO;kFAC3B;uEADU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAA2C;8DAClD,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtC,8OAAC;4CAAI,WAAW,CAAC,4DAA4D,EAC3E,SAAS,MAAM,CAAC,MAAM,KAAK,KAAK,QAAQ,6BAA6B,mBACrE;sDACC,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;oDAAkB,WAAU;;sEAC3B,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,SAAS,MAAM,CAAC,QAAQ,CAAC;4DAClC,UAAU,IAAM,kBAAkB;;;;;;sEAEpC,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;mDAPpC;;;;;;;;;;;;;;;;8CAclB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAA+C;;;;;;sDAGrF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAM;4CACN,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,UAAU;4CAC1B,UAAU;;;;;;;;;;;;8CAKd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAa,WAAU;;gDAA+C;8DAC9E,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtC,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAM;4CACN,QAAQ;4CACR,WAAW,CAAC,6FAA6F,EACvG,CAAC,SAAS,UAAU,IAAI,QAAQ,6BAA6B,mBAC7D;4CACF,aAAY;4CACZ,OAAO,SAAS,UAAU;4CAC1B,UAAU;;;;;;;;;;;;8CAKd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAS,WAAU;sDAA+C;;;;;;sDAGjF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,MAAM;4CACtB,UAAU;;;;;;;;;;;;gCAIb,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAQ;gDAAU,WAAU;0DAA0B;;;;;;;;;;;sDAI9E,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,+FAA+F,EACzG,YACI,iDACA,sIACJ;sDAED,0BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;oDAAqC;;;;;;uDAItD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "file": "user.js", "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/layout/admin-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport {\n  LayoutDashboard,\n  Users,\n  FileText,\n  UserCheck,\n  Settings,\n  BarChart3,\n  Menu,\n  X,\n  LogOut,\n  Home,\n  Clock,\n  Plus,\n  Activity\n} from 'lucide-react'\n\nconst sidebarItems = [\n  { name: '仪表板', href: '/admin', icon: LayoutDashboard },\n  { name: '用户管理', href: '/admin/users', icon: Users },\n  { name: '作品管理', href: '/admin/projects', icon: FileText },\n  { name: '团队管理', href: '/admin/team-members', icon: UserCheck },\n  { name: '申请审批', href: '/admin/applications', icon: Clock },\n  { name: '排班管理', href: '/admin/schedule', icon: Clock },\n  { name: '博客管理', href: '/admin/posts', icon: FileText },\n  { name: '数据分析', href: '/admin/analytics', icon: BarChart3 },\n  { name: '系统监控', href: '/admin/system', icon: Activity },\n  { name: '系统设置', href: '/admin/settings', icon: Settings },\n]\n\ninterface AdminLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AdminLayout({ children }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { data: session } = useSession()\n\n  return (\n    <div className=\"admin-layout min-h-screen bg-gray-50\">\n      {/* Mobile sidebar backdrop */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        admin-sidebar fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-enhanced transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col lg:h-screen\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-white flex-shrink-0\">\n          <Link href=\"/admin\" className=\"flex items-center\">\n            <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\n              <span className=\"text-xl font-bold\">管理后台</span>\n            </div>\n          </Link>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-colors\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        <nav className=\"flex-1 mt-6 px-3 overflow-y-auto\">\n          <div className=\"space-y-1\">\n            {sidebarItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n              \n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200\n                    ${isActive \n                      ? 'bg-blue-50 text-blue-700 shadow-sm' \n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                    }\n                  `}\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <Icon className={`\n                    mr-3 h-5 w-5 transition-colors\n                    ${isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}\n                  `} />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </div>\n\n          <div className=\"mt-8 pt-6 border-t border-gray-200\">\n            <div className=\"space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"group flex items-center px-3 py-3 text-sm font-medium text-gray-700 rounded-xl hover:bg-gray-50 hover:text-gray-900 transition-all duration-200\"\n              >\n                <Home className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                返回前台\n              </Link>\n              <button\n                onClick={() => signOut()}\n                className=\"w-full group flex items-center px-3 py-3 text-sm font-medium text-gray-700 rounded-xl hover:bg-gray-50 hover:text-gray-900 transition-all duration-200\"\n              >\n                <LogOut className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                退出登录\n              </button>\n            </div>\n          </div>\n        </nav>\n      </div>\n\n      {/* Main content */}\n      <div className=\"admin-main flex-1 flex flex-col min-h-screen\">\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200 px-4 sm:px-6 lg:px-8 flex-shrink-0 sticky top-0 z-30\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-colors\"\n              >\n                <Menu className=\"w-5 h-5\" />\n              </button>\n              <h1 className=\"ml-2 lg:ml-0 text-2xl font-semibold text-gray-900\">\n                {sidebarItems.find(item => item.href === pathname)?.name || '管理后台'}\n              </h1>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"hidden sm:flex items-center text-sm text-gray-500\">\n                欢迎, {session?.user?.name}\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-medium\">\n                  {session?.user?.name?.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 p-4 sm:p-6 lg:p-8 overflow-auto\">\n          <div className=\"animate-fade-in max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAuBA,MAAM,eAAe;IACnB;QAAE,MAAM;QAAO,MAAM;QAAU,MAAM,4NAAA,CAAA,kBAAe;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,gNAAA,CAAA,YAAS;IAAC;IAC7D;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACzD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,kNAAA,CAAA,YAAS;IAAC;IAC1D;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACzD;AAMc,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,8OAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,cAAc,kBAAkB,oBAAoB;MACxD,CAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAC5B,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;;;;;0CAGxC,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC;oCACjB,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC;;oBAEV,EAAE,WACE,uCACA,qDACH;kBACH,CAAC;wCACD,SAAS,IAAM,eAAe;;0DAE9B,8OAAC;gDAAK,WAAW,CAAC;;oBAEhB,EAAE,WAAW,kBAAkB,0CAA0C;kBAC3E,CAAC;;;;;;4CACA,KAAK,IAAI;;uCAfL,KAAK,IAAI;;;;;gCAkBpB;;;;;;0CAGF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAyD;;;;;;;sDAG3E,8OAAC;4CACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4CACrB,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDACX,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,QAAQ;;;;;;;;;;;;8CAIhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAoD;gDAC5D,SAAS,MAAM;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,SAAS,MAAM,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport AdminLayout from '@/components/layout/admin-layout'\nimport {\n  Users,\n  FileText,\n  UserCheck,\n  Settings,\n  BarChart3,\n  Eye,\n  Plus,\n  Clock,\n  CheckCircle,\n  XCircle,\n  BookOpen\n} from 'lucide-react'\n\ninterface DashboardStats {\n  totalUsers: number\n  totalProjects: number\n  pendingApplications: number\n  totalTeamMembers: number\n  totalPosts: number\n  publishedPosts: number\n  recentProjects: any[]\n  recentApplications: any[]\n  recentPosts: any[]\n  todayStats: {\n    newUsers: number\n    newProjects: number\n    newApplications: number\n  }\n  growthRates: {\n    users: number\n    projects: number\n    applications: number\n  }\n}\n\nexport default function AdminDashboard() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [stats, setStats] = useState<DashboardStats | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    if (status === 'loading') return\n\n    if (!session || session.user.role !== 'ADMIN') {\n      router.push('/')\n      return\n    }\n\n    fetchDashboardStats()\n  }, [session, status, router])\n\n  const fetchDashboardStats = async () => {\n    try {\n      const response = await fetch('/api/admin/dashboard')\n      if (response.ok) {\n        const data = await response.json()\n        setStats(data)\n      }\n    } catch (error) {\n      console.error('Failed to fetch dashboard stats:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (status === 'loading' || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session || session.user.role !== 'ADMIN') {\n    return null\n  }\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-8\">\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\">\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6 card-hover\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Users className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">总用户数</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {stats?.totalUsers || 0}\n                  </p>\n                </div>\n              </div>\n              {stats?.todayStats?.newUsers > 0 && (\n                <div className=\"text-right\">\n                  <p className=\"text-xs text-green-600\">今日新增</p>\n                  <p className=\"text-sm font-semibold text-green-600\">\n                    +{stats.todayStats.newUsers}\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6 card-hover\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-green-100 rounded-xl\">\n                  <FileText className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">总作品数</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {stats?.totalProjects || 0}\n                  </p>\n                </div>\n              </div>\n              {stats?.todayStats?.newProjects > 0 && (\n                <div className=\"text-right\">\n                  <p className=\"text-xs text-green-600\">今日新增</p>\n                  <p className=\"text-sm font-semibold text-green-600\">\n                    +{stats.todayStats.newProjects}\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6 card-hover\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-orange-100 rounded-xl\">\n                  <Clock className=\"h-6 w-6 text-orange-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">待审核申请</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {stats?.pendingApplications || 0}\n                  </p>\n                </div>\n              </div>\n              {stats?.todayStats?.newApplications > 0 && (\n                <div className=\"text-right\">\n                  <p className=\"text-xs text-orange-600\">今日新增</p>\n                  <p className=\"text-sm font-semibold text-orange-600\">\n                    +{stats.todayStats.newApplications}\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6 card-hover\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-indigo-100 rounded-xl\">\n                <BookOpen className=\"h-6 w-6 text-indigo-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">发布文章</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats?.publishedPosts || 0}\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  总计 {stats?.totalPosts || 0} 篇\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6 card-hover\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-purple-100 rounded-xl\">\n                <UserCheck className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">团队成员</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats?.totalTeamMembers || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"bg-white rounded-lg shadow mb-8\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">快速操作</h2>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n              <Link href=\"/admin/posts/new\">\n                <Button className=\"w-full justify-start\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  新建文章\n                </Button>\n              </Link>\n              <Link href=\"/admin/projects/new\">\n                <Button className=\"w-full justify-start\" variant=\"outline\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  新建项目\n                </Button>\n              </Link>\n              <Link href=\"/admin/team-members/new\">\n                <Button className=\"w-full justify-start\" variant=\"outline\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  新建团队成员\n                </Button>\n              </Link>\n              <Link href=\"/admin/applications\">\n                <Button className=\"w-full justify-start\" variant=\"outline\">\n                  <Eye className=\"w-4 h-4 mr-2\" />\n                  查看申请\n                </Button>\n              </Link>\n              <Link href=\"/admin/schedule/auto\">\n                <Button className=\"w-full justify-start\" variant=\"outline\">\n                  <Clock className=\"w-4 h-4 mr-2\" />\n                  自动排班\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Recent Projects */}\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"p-6 border-b border-gray-200\">\n              <div className=\"flex justify-between items-center\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">最新作品</h2>\n                <Link href=\"/admin/projects\">\n                  <Button variant=\"outline\" size=\"sm\">\n                    查看全部\n                  </Button>\n                </Link>\n              </div>\n            </div>\n            <div className=\"p-6\">\n              {stats?.recentProjects?.length ? (\n                <div className=\"space-y-4\">\n                  {stats.recentProjects.slice(0, 5).map((project: any) => (\n                    <div key={project.id} className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"font-medium text-gray-900\">{project.title}</p>\n                        <p className=\"text-sm text-gray-500\">\n                          by {project.author.name}\n                        </p>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-sm text-gray-500\">\n                          {project.viewCount} 浏览\n                        </span>\n                        <Link href={`/admin/projects/${project.id}`}>\n                          <Button size=\"sm\" variant=\"outline\">\n                            编辑\n                          </Button>\n                        </Link>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-gray-500 text-center py-4\">暂无作品</p>\n              )}\n            </div>\n          </div>\n\n          {/* Recent Applications */}\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"p-6 border-b border-gray-200\">\n              <div className=\"flex justify-between items-center\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">最新申请</h2>\n                <Link href=\"/admin/applications\">\n                  <Button variant=\"outline\" size=\"sm\">\n                    查看全部\n                  </Button>\n                </Link>\n              </div>\n            </div>\n            <div className=\"p-6\">\n              {stats?.recentApplications?.length ? (\n                <div className=\"space-y-4\">\n                  {stats.recentApplications.slice(0, 5).map((application: any) => (\n                    <div key={application.id} className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"font-medium text-gray-900\">{application.name}</p>\n                        <p className=\"text-sm text-gray-500\">\n                          申请 {application.position}\n                        </p>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        {application.status === 'PENDING' && (\n                          <Clock className=\"w-4 h-4 text-orange-500\" />\n                        )}\n                        {application.status === 'APPROVED' && (\n                          <CheckCircle className=\"w-4 h-4 text-green-500\" />\n                        )}\n                        {application.status === 'REJECTED' && (\n                          <XCircle className=\"w-4 h-4 text-red-500\" />\n                        )}\n                        <Link href={`/admin/applications/${application.id}`}>\n                          <Button size=\"sm\" variant=\"outline\">\n                            查看\n                          </Button>\n                        </Link>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-gray-500 text-center py-4\">暂无申请</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation Menu */}\n        <div className=\"mt-8 bg-white rounded-lg shadow\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">管理功能</h2>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <Link href=\"/admin/projects\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <FileText className=\"w-6 h-6 text-blue-600 mr-3\" />\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">作品管理</h3>\n                    <p className=\"text-sm text-gray-500\">管理所有作品内容</p>\n                  </div>\n                </div>\n              </Link>\n\n              <Link href=\"/admin/team-members\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <UserCheck className=\"w-6 h-6 text-green-600 mr-3\" />\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">团队管理</h3>\n                    <p className=\"text-sm text-gray-500\">管理团队成员信息</p>\n                  </div>\n                </div>\n              </Link>\n\n              <Link href=\"/admin/applications\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-6 h-6 text-orange-600 mr-3\" />\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">申请审批</h3>\n                    <p className=\"text-sm text-gray-500\">处理加入申请</p>\n                  </div>\n                </div>\n              </Link>\n\n              <Link href=\"/admin/users\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Users className=\"w-6 h-6 text-purple-600 mr-3\" />\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">用户管理</h3>\n                    <p className=\"text-sm text-gray-500\">管理所有用户</p>\n                  </div>\n                </div>\n              </Link>\n\n              <Link href=\"/admin/analytics\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <BarChart3 className=\"w-6 h-6 text-indigo-600 mr-3\" />\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">数据分析</h3>\n                    <p className=\"text-sm text-gray-500\">查看平台数据</p>\n                  </div>\n                </div>\n              </Link>\n\n              <Link href=\"/admin/settings\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Settings className=\"w-6 h-6 text-gray-600 mr-3\" />\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">系统设置</h3>\n                    <p className=\"text-sm text-gray-500\">配置系统参数</p>\n                  </div>\n                </div>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AA4Ce,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;QAE1B,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,WAAW,aAAa,WAAW;QACrC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QAC7C,OAAO;IACT;IAEA,qBACE,8OAAC,+IAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,OAAO,cAAc;;;;;;;;;;;;;;;;;;oCAI3B,OAAO,YAAY,WAAW,mBAC7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;;oDAAuC;oDAChD,MAAM,UAAU,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;oCAI9B,OAAO,YAAY,cAAc,mBAChC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;;oDAAuC;oDAChD,MAAM,UAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sCAOxC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,OAAO,uBAAuB;;;;;;;;;;;;;;;;;;oCAIpC,OAAO,YAAY,kBAAkB,mBACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;0DACvC,8OAAC;gDAAE,WAAU;;oDAAwC;oDACjD,MAAM,UAAU,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAO5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,OAAO,kBAAkB;;;;;;0DAE5B,8OAAC;gDAAE,WAAU;;oDAAwB;oDAC/B,OAAO,cAAc;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAMnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,OAAO,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIrC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAuB,SAAQ;;8DAC/C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIrC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAuB,SAAQ;;8DAC/C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIrC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAuB,SAAQ;;8DAC/C,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIpC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAuB,SAAQ;;8DAC/C,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ5C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;8DAAK;;;;;;;;;;;;;;;;;;;;;;8CAM1C,8OAAC;oCAAI,WAAU;8CACZ,OAAO,gBAAgB,uBACtB,8OAAC;wCAAI,WAAU;kDACZ,MAAM,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACrC,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAA6B,QAAQ,KAAK;;;;;;0EACvD,8OAAC;gEAAE,WAAU;;oEAAwB;oEAC/B,QAAQ,MAAM,CAAC,IAAI;;;;;;;;;;;;;kEAG3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEACb,QAAQ,SAAS;oEAAC;;;;;;;0EAErB,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;0EACzC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;8EAAU;;;;;;;;;;;;;;;;;;+CAZhC,QAAQ,EAAE;;;;;;;;;6DAqBxB,8OAAC;wCAAE,WAAU;kDAAiC;;;;;;;;;;;;;;;;;sCAMpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;8DAAK;;;;;;;;;;;;;;;;;;;;;;8CAM1C,8OAAC;oCAAI,WAAU;8CACZ,OAAO,oBAAoB,uBAC1B,8OAAC;wCAAI,WAAU;kDACZ,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,4BACzC,8OAAC;gDAAyB,WAAU;;kEAClC,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAA6B,YAAY,IAAI;;;;;;0EAC1D,8OAAC;gEAAE,WAAU;;oEAAwB;oEAC/B,YAAY,QAAQ;;;;;;;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;4DACZ,YAAY,MAAM,KAAK,2BACtB,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAElB,YAAY,MAAM,KAAK,4BACtB,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAExB,YAAY,MAAM,KAAK,4BACtB,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EAErB,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,oBAAoB,EAAE,YAAY,EAAE,EAAE;0EACjD,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;8EAAU;;;;;;;;;;;;;;;;;;+CAlBhC,YAAY,EAAE;;;;;;;;;6DA2B5B,8OAAC;wCAAE,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;;;;;;8BAOtD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAkB,WAAU;kDACrC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAK3C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAsB,WAAU;kDACzC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAK3C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAsB,WAAU;kDACzC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAK3C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAClC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAK3C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;kDACtC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAK3C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAkB,WAAU;kDACrC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}]}
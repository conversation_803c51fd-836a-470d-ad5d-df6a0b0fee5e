{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/layout/admin-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport {\n  LayoutDashboard,\n  Users,\n  FileText,\n  UserCheck,\n  Settings,\n  BarChart3,\n  Menu,\n  X,\n  LogOut,\n  Home,\n  Clock,\n  Plus,\n  Activity\n} from 'lucide-react'\n\nconst sidebarItems = [\n  { name: '仪表板', href: '/admin', icon: LayoutDashboard },\n  { name: '用户管理', href: '/admin/users', icon: Users },\n  { name: '作品管理', href: '/admin/projects', icon: FileText },\n  { name: '团队管理', href: '/admin/team-members', icon: UserCheck },\n  { name: '申请审批', href: '/admin/applications', icon: Clock },\n  { name: '排班管理', href: '/admin/schedule', icon: Clock },\n  { name: '博客管理', href: '/admin/posts', icon: FileText },\n  { name: '数据分析', href: '/admin/analytics', icon: BarChart3 },\n  { name: '系统监控', href: '/admin/system', icon: Activity },\n  { name: '系统设置', href: '/admin/settings', icon: Settings },\n]\n\ninterface AdminLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AdminLayout({ children }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { data: session } = useSession()\n\n  return (\n    <div className=\"admin-layout min-h-screen bg-gray-50\">\n      {/* Mobile sidebar backdrop */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        admin-sidebar fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-enhanced transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col lg:h-screen\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-white flex-shrink-0\">\n          <Link href=\"/admin\" className=\"flex items-center\">\n            <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\n              <span className=\"text-xl font-bold\">管理后台</span>\n            </div>\n          </Link>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-colors\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        <nav className=\"flex-1 mt-6 px-3 overflow-y-auto\">\n          <div className=\"space-y-1\">\n            {sidebarItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n              \n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200\n                    ${isActive \n                      ? 'bg-blue-50 text-blue-700 shadow-sm' \n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                    }\n                  `}\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <Icon className={`\n                    mr-3 h-5 w-5 transition-colors\n                    ${isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}\n                  `} />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </div>\n\n          <div className=\"mt-8 pt-6 border-t border-gray-200\">\n            <div className=\"space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"group flex items-center px-3 py-3 text-sm font-medium text-gray-700 rounded-xl hover:bg-gray-50 hover:text-gray-900 transition-all duration-200\"\n              >\n                <Home className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                返回前台\n              </Link>\n              <button\n                onClick={() => signOut()}\n                className=\"w-full group flex items-center px-3 py-3 text-sm font-medium text-gray-700 rounded-xl hover:bg-gray-50 hover:text-gray-900 transition-all duration-200\"\n              >\n                <LogOut className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                退出登录\n              </button>\n            </div>\n          </div>\n        </nav>\n      </div>\n\n      {/* Main content */}\n      <div className=\"admin-main flex-1 flex flex-col min-h-screen\">\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200 px-4 sm:px-6 lg:px-8 flex-shrink-0 sticky top-0 z-30\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-colors\"\n              >\n                <Menu className=\"w-5 h-5\" />\n              </button>\n              <h1 className=\"ml-2 lg:ml-0 text-2xl font-semibold text-gray-900\">\n                {sidebarItems.find(item => item.href === pathname)?.name || '管理后台'}\n              </h1>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"hidden sm:flex items-center text-sm text-gray-500\">\n                欢迎, {session?.user?.name}\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-medium\">\n                  {session?.user?.name?.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 p-4 sm:p-6 lg:p-8 overflow-auto\">\n          <div className=\"animate-fade-in max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAuBA,MAAM,eAAe;IACnB;QAAE,MAAM;QAAO,MAAM;QAAU,MAAM,4NAAA,CAAA,kBAAe;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,gNAAA,CAAA,YAAS;IAAC;IAC7D;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACzD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,kNAAA,CAAA,YAAS;IAAC;IAC1D;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACzD;AAMc,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,8OAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,cAAc,kBAAkB,oBAAoB;MACxD,CAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAC5B,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;;;;;0CAGxC,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC;oCACjB,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC;;oBAEV,EAAE,WACE,uCACA,qDACH;kBACH,CAAC;wCACD,SAAS,IAAM,eAAe;;0DAE9B,8OAAC;gDAAK,WAAW,CAAC;;oBAEhB,EAAE,WAAW,kBAAkB,0CAA0C;kBAC3E,CAAC;;;;;;4CACA,KAAK,IAAI;;uCAfL,KAAK,IAAI;;;;;gCAkBpB;;;;;;0CAGF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAyD;;;;;;;sDAG3E,8OAAC;4CACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4CACrB,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDACX,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,QAAQ;;;;;;;;;;;;8CAIhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAoD;gDAC5D,SAAS,MAAM;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,SAAS,MAAM,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/admin/system/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport AdminLayout from '@/components/layout/admin-layout'\nimport { Button } from '@/components/ui/button'\nimport { \n  Server, \n  Database, \n  Wifi, \n  HardDrive, \n  Cpu, \n  Activity,\n  AlertTriangle,\n  CheckCircle,\n  RefreshCw,\n  Download,\n  Upload\n} from 'lucide-react'\n\ninterface SystemStatus {\n  status: 'healthy' | 'warning' | 'error'\n  timestamp: string\n  uptime: number\n  version: string\n  environment: string\n  performance: {\n    apiResponseTime: number\n    dbResponseTime: number\n    memoryUsage: number\n    cpuCores: number\n    loadAverage: number\n  }\n  resources: {\n    memory: {\n      total: number\n      used: number\n      free: number\n      usage: number\n    }\n    cpu: {\n      cores: number\n      model: string\n      load: number[]\n    }\n    disk: {\n      database: number\n    }\n  }\n  services: Array<{\n    name: string\n    status: string\n    responseTime: number\n    details: string\n  }>\n  database: {\n    status: string\n    responseTime: number\n    records: {\n      users: number\n      projects: number\n      posts: number\n      applications: number\n      total: number\n    }\n    size: number\n  }\n  system: {\n    platform: string\n    architecture: string\n    nodeVersion: string\n    uptime: number\n    startTime: string\n  }\n}\n\nexport default function SystemPage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())\n\n  useEffect(() => {\n    if (status === 'loading') return\n    if (!session || session.user.role !== 'ADMIN') {\n      router.push('/')\n      return\n    }\n    fetchSystemStatus()\n    \n    // 每30秒更新一次状态\n    const interval = setInterval(fetchSystemStatus, 30000)\n    return () => clearInterval(interval)\n  }, [session, status, router])\n\n  const fetchSystemStatus = async () => {\n    try {\n      const response = await fetch('/api/admin/system/status')\n      if (response.ok) {\n        const data = await response.json()\n        setSystemStatus(data)\n        setLastUpdate(new Date())\n      } else {\n        console.error('Failed to fetch system status')\n      }\n    } catch (error) {\n      console.error('Failed to fetch system status:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'healthy':\n      case 'online':\n      case 'connected':\n        return 'text-green-600 bg-green-100'\n      case 'warning':\n      case 'slow':\n        return 'text-yellow-600 bg-yellow-100'\n      case 'error':\n      case 'offline':\n      case 'disconnected':\n        return 'text-red-600 bg-red-100'\n      default:\n        return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'online':\n      case 'connected':\n        return <CheckCircle className=\"w-4 h-4\" />\n      case 'warning':\n      case 'slow':\n        return <AlertTriangle className=\"w-4 h-4\" />\n      case 'offline':\n      case 'disconnected':\n        return <AlertTriangle className=\"w-4 h-4\" />\n      default:\n        return <Activity className=\"w-4 h-4\" />\n    }\n  }\n\n  const getPerformanceColor = (value: number, type: 'cpu' | 'memory' | 'disk' | 'response') => {\n    const thresholds = {\n      cpu: { warning: 70, danger: 90 },\n      memory: { warning: 80, danger: 95 },\n      disk: { warning: 80, danger: 95 },\n      response: { warning: 200, danger: 500 }\n    }\n\n    const threshold = thresholds[type]\n    if (value >= threshold.danger) return 'bg-red-500'\n    if (value >= threshold.warning) return 'bg-yellow-500'\n    return 'bg-green-500'\n  }\n\n  if (status === 'loading' || isLoading) {\n    return (\n      <AdminLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"loading-spinner w-8 h-8\"></div>\n        </div>\n      </AdminLayout>\n    )\n  }\n\n  if (!session || session.user.role !== 'ADMIN') {\n    return null\n  }\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">系统监控</h2>\n            <p className=\"text-gray-600\">实时监控系统状态和性能</p>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-500\">\n              最后更新: {lastUpdate.toLocaleTimeString('zh-CN')}\n            </span>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={fetchSystemStatus}\n              className=\"rounded-xl\"\n            >\n              <RefreshCw className=\"w-4 h-4 mr-2\" />\n              刷新\n            </Button>\n          </div>\n        </div>\n\n        {systemStatus && (\n          <>\n            {/* 系统状态卡片 */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* 服务器状态 */}\n              <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n                    <Server className=\"w-5 h-5 mr-2\" />\n                    服务器状态\n                  </h3>\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(systemStatus.status)}`}>\n                    {getStatusIcon(systemStatus.status)}\n                    <span className=\"ml-1\">\n                      {systemStatus.status === 'healthy' ? '健康' : systemStatus.status === 'warning' ? '警告' : '错误'}\n                    </span>\n                  </span>\n                </div>\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">运行时间:</span>\n                    <span className=\"font-medium\">{Math.floor(systemStatus.uptime / 3600)}小时 {Math.floor((systemStatus.uptime % 3600) / 60)}分钟</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">版本:</span>\n                    <span className=\"font-medium\">{systemStatus.version}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">环境:</span>\n                    <span className=\"font-medium\">{systemStatus.environment}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">平台:</span>\n                    <span className=\"font-medium\">{systemStatus.system?.platform} {systemStatus.system?.architecture}</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* 数据库状态 */}\n              <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n                    <Database className=\"w-5 h-5 mr-2\" />\n                    数据库状态\n                  </h3>\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(systemStatus.database.status)}`}>\n                    {getStatusIcon(systemStatus.database.status)}\n                    <span className=\"ml-1\">\n                      {systemStatus.database.status === 'healthy' ? '健康' : '异常'}\n                    </span>\n                  </span>\n                </div>\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">响应时间:</span>\n                    <span className=\"font-medium\">{systemStatus.database.responseTime}ms</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">数据库大小:</span>\n                    <span className=\"font-medium\">{systemStatus.database.size}MB</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">总记录数:</span>\n                    <span className=\"font-medium\">{systemStatus.database.records?.total || 0}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">用户数:</span>\n                    <span className=\"font-medium\">{systemStatus.database.records?.users || 0}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 性能监控 */}\n            <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <Activity className=\"w-5 h-5 mr-2\" />\n                性能监控\n              </h3>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <Cpu className=\"w-8 h-8 text-blue-600\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 mb-1\">\n                    {systemStatus.performance.cpu}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-2\">CPU使用率</div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`h-2 rounded-full transition-all duration-300 ${getPerformanceColor(systemStatus.performance.cpu, 'cpu')}`}\n                      style={{ width: `${systemStatus.performance.cpu}%` }}\n                    ></div>\n                  </div>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <HardDrive className=\"w-8 h-8 text-green-600\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 mb-1\">\n                    {systemStatus.performance.memory}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-2\">内存使用率</div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`h-2 rounded-full transition-all duration-300 ${getPerformanceColor(systemStatus.performance.memory, 'memory')}`}\n                      style={{ width: `${systemStatus.performance.memory}%` }}\n                    ></div>\n                  </div>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <HardDrive className=\"w-8 h-8 text-purple-600\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 mb-1\">\n                    {systemStatus.performance.disk}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-2\">磁盘使用率</div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`h-2 rounded-full transition-all duration-300 ${getPerformanceColor(systemStatus.performance.disk, 'disk')}`}\n                      style={{ width: `${systemStatus.performance.disk}%` }}\n                    ></div>\n                  </div>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <Wifi className=\"w-8 h-8 text-orange-600\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 mb-1\">\n                    {systemStatus.performance.responseTime}ms\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-2\">响应时间</div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`h-2 rounded-full transition-all duration-300 ${getPerformanceColor(systemStatus.performance.responseTime, 'response')}`}\n                      style={{ width: `${Math.min(systemStatus.performance.responseTime / 5, 100)}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 功能状态 */}\n            <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">功能状态</h3>\n              \n              <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\">\n                {Object.entries(systemStatus.features).map(([key, enabled]) => (\n                  <div key={key} className=\"text-center p-4 border border-gray-200 rounded-lg\">\n                    <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full mb-2 ${\n                      enabled ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'\n                    }`}>\n                      {enabled ? <CheckCircle className=\"w-4 h-4\" /> : <AlertTriangle className=\"w-4 h-4\" />}\n                    </div>\n                    <div className=\"text-sm font-medium text-gray-900 capitalize\">\n                      {key === 'registration' ? '用户注册' :\n                       key === 'blog' ? '博客功能' :\n                       key === 'projects' ? '作品展示' :\n                       key === 'team' ? '团队介绍' :\n                       key === 'apply' ? '申请功能' : key}\n                    </div>\n                    <div className={`text-xs mt-1 ${enabled ? 'text-green-600' : 'text-red-600'}`}>\n                      {enabled ? '启用' : '禁用'}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* 快速操作 */}\n            <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">快速操作</h3>\n              \n              <div className=\"flex flex-wrap gap-4\">\n                <Button variant=\"outline\" className=\"btn-enhanced rounded-xl\">\n                  <Download className=\"w-4 h-4 mr-2\" />\n                  导出数据\n                </Button>\n                <Button variant=\"outline\" className=\"btn-enhanced rounded-xl\">\n                  <Upload className=\"w-4 h-4 mr-2\" />\n                  导入数据\n                </Button>\n                <Button variant=\"outline\" className=\"btn-enhanced rounded-xl\">\n                  <Database className=\"w-4 h-4 mr-2\" />\n                  备份数据库\n                </Button>\n                <Button variant=\"outline\" className=\"btn-enhanced rounded-xl\">\n                  <RefreshCw className=\"w-4 h-4 mr-2\" />\n                  重启服务\n                </Button>\n              </div>\n            </div>\n          </>\n        )}\n      </div>\n    </AdminLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AA6Ee,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;QAC1B,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,IAAI,CAAC;YACZ;QACF;QACA;QAEA,aAAa;QACb,MAAM,WAAW,YAAY,mBAAmB;QAChD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB;gBAChB,cAAc,IAAI;YACpB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,sBAAsB,CAAC,OAAe;QAC1C,MAAM,aAAa;YACjB,KAAK;gBAAE,SAAS;gBAAI,QAAQ;YAAG;YAC/B,QAAQ;gBAAE,SAAS;gBAAI,QAAQ;YAAG;YAClC,MAAM;gBAAE,SAAS;gBAAI,QAAQ;YAAG;YAChC,UAAU;gBAAE,SAAS;gBAAK,QAAQ;YAAI;QACxC;QAEA,MAAM,YAAY,UAAU,CAAC,KAAK;QAClC,IAAI,SAAS,UAAU,MAAM,EAAE,OAAO;QACtC,IAAI,SAAS,UAAU,OAAO,EAAE,OAAO;QACvC,OAAO;IACT;IAEA,IAAI,WAAW,aAAa,WAAW;QACrC,qBACE,8OAAC,+IAAA,CAAA,UAAW;sBACV,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QAC7C,OAAO;IACT;IAEA,qBACE,8OAAC,+IAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAwB;wCAC/B,WAAW,kBAAkB,CAAC;;;;;;;8CAEvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;gBAM3C,8BACC;;sCAEE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGrC,8OAAC;oDAAK,WAAW,CAAC,oEAAoE,EAAE,eAAe,aAAa,MAAM,GAAG;;wDAC1H,cAAc,aAAa,MAAM;sEAClC,8OAAC;4DAAK,WAAU;sEACb,aAAa,MAAM,KAAK,YAAY,OAAO,aAAa,MAAM,KAAK,YAAY,OAAO;;;;;;;;;;;;;;;;;;sDAI7F,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAe,KAAK,KAAK,CAAC,aAAa,MAAM,GAAG;gEAAM;gEAAI,KAAK,KAAK,CAAC,AAAC,aAAa,MAAM,GAAG,OAAQ;gEAAI;;;;;;;;;;;;;8DAE1H,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,aAAa,OAAO;;;;;;;;;;;;8DAErD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,aAAa,WAAW;;;;;;;;;;;;8DAEzD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAe,aAAa,MAAM,EAAE;gEAAS;gEAAE,aAAa,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;8CAM1F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC;oDAAK,WAAW,CAAC,oEAAoE,EAAE,eAAe,aAAa,QAAQ,CAAC,MAAM,GAAG;;wDACnI,cAAc,aAAa,QAAQ,CAAC,MAAM;sEAC3C,8OAAC;4DAAK,WAAU;sEACb,aAAa,QAAQ,CAAC,MAAM,KAAK,YAAY,OAAO;;;;;;;;;;;;;;;;;;sDAI3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAe,aAAa,QAAQ,CAAC,YAAY;gEAAC;;;;;;;;;;;;;8DAEpE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAe,aAAa,QAAQ,CAAC,IAAI;gEAAC;;;;;;;;;;;;;8DAE5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,aAAa,QAAQ,CAAC,OAAO,EAAE,SAAS;;;;;;;;;;;;8DAEzE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,aAAa,QAAQ,CAAC,OAAO,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;wDACZ,aAAa,WAAW,CAAC,GAAG;wDAAC;;;;;;;8DAEhC,8OAAC;oDAAI,WAAU;8DAA6B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAW,CAAC,6CAA6C,EAAE,oBAAoB,aAAa,WAAW,CAAC,GAAG,EAAE,QAAQ;wDACrH,OAAO;4DAAE,OAAO,GAAG,aAAa,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;sDAKzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;wDACZ,aAAa,WAAW,CAAC,MAAM;wDAAC;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;8DAA6B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAW,CAAC,6CAA6C,EAAE,oBAAoB,aAAa,WAAW,CAAC,MAAM,EAAE,WAAW;wDAC3H,OAAO;4DAAE,OAAO,GAAG,aAAa,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;sDAK5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;wDACZ,aAAa,WAAW,CAAC,IAAI;wDAAC;;;;;;;8DAEjC,8OAAC;oDAAI,WAAU;8DAA6B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAW,CAAC,6CAA6C,EAAE,oBAAoB,aAAa,WAAW,CAAC,IAAI,EAAE,SAAS;wDACvH,OAAO;4DAAE,OAAO,GAAG,aAAa,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;sDAK1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDAAI,WAAU;;wDACZ,aAAa,WAAW,CAAC,YAAY;wDAAC;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;8DAA6B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAW,CAAC,6CAA6C,EAAE,oBAAoB,aAAa,WAAW,CAAC,YAAY,EAAE,aAAa;wDACnI,OAAO;4DAAE,OAAO,GAAG,KAAK,GAAG,CAAC,aAAa,WAAW,CAAC,YAAY,GAAG,GAAG,KAAK,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,aAAa,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,iBACxD,8OAAC;4CAAc,WAAU;;8DACvB,8OAAC;oDAAI,WAAW,CAAC,kEAAkE,EACjF,UAAU,gCAAgC,2BAC1C;8DACC,wBAAU,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAAe,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE5E,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,iBAAiB,SACzB,QAAQ,SAAS,SACjB,QAAQ,aAAa,SACrB,QAAQ,SAAS,SACjB,QAAQ,UAAU,SAAS;;;;;;8DAE9B,8OAAC;oDAAI,WAAW,CAAC,aAAa,EAAE,UAAU,mBAAmB,gBAAgB;8DAC1E,UAAU,OAAO;;;;;;;2CAdZ;;;;;;;;;;;;;;;;sCAsBhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;;8DAClC,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;;8DAClC,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;;8DAClC,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;;8DAClC,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxD", "debugId": null}}]}
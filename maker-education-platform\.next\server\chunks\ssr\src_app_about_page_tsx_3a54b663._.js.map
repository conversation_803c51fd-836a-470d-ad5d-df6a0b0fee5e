{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/about/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { BookOpen, Users, Lightbulb, Award, Cpu, Printer, Wrench, Microscope, TrendingUp } from 'lucide-react'\nimport { useSettings } from '@/hooks/useSettings'\n\nexport default function About() {\n  const { settings } = useSettings()\n  const [mainEquipment, setMainEquipment] = useState<Array<{id: string, name: string, quantity: number, description: string}>>([])\n  const [classroomSpecs, setClassroomSpecs] = useState<Array<{id: string, name: string, value: string, unit: string}>>([])\n\n  useEffect(() => {\n    // 解析主要设备数据\n    if (settings.main_equipment) {\n      try {\n        const equipmentData = JSON.parse(settings.main_equipment)\n        setMainEquipment(equipmentData)\n      } catch (e) {\n        setMainEquipment([])\n      }\n    }\n\n    // 解析教室规格数据\n    if (settings.classroom_specs) {\n      try {\n        const specsData = JSON.parse(settings.classroom_specs)\n        setClassroomSpecs(specsData)\n      } catch (e) {\n        setClassroomSpecs([])\n      }\n    }\n  }, [settings])\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              关于我们的创客教室\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              专业的STEAM教育实验室，致力于培养学生的创新思维和实践能力\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* 教育理念 */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              STEAM教育理念\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Science（科学）、Technology（技术）、Engineering（工程）、Arts（艺术）、Mathematics（数学）\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Microscope className=\"h-10 w-10 text-blue-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Science</h3>\n              <p className=\"text-gray-600 text-sm\">科学探索与实验</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Cpu className=\"h-10 w-10 text-green-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Technology</h3>\n              <p className=\"text-gray-600 text-sm\">技术应用与创新</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Wrench className=\"h-10 w-10 text-purple-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Engineering</h3>\n              <p className=\"text-gray-600 text-sm\">工程设计与制作</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Award className=\"h-10 w-10 text-orange-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Arts</h3>\n              <p className=\"text-gray-600 text-sm\">艺术创作与设计</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <BookOpen className=\"h-10 w-10 text-red-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Mathematics</h3>\n              <p className=\"text-gray-600 text-sm\">数学建模与计算</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 统计数据 */}\n      <section className=\"py-16 bg-gradient-to-r from-blue-600 to-indigo-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center text-white\">\n            <div className=\"animate-fade-in\">\n              <div className=\"text-4xl md:text-5xl font-bold mb-2\">{settings.total_members || 50}+</div>\n              <div className=\"text-blue-100\">总成员数</div>\n            </div>\n            <div className=\"animate-fade-in\">\n              <div className=\"text-4xl md:text-5xl font-bold mb-2\">{settings.total_projects || 120}+</div>\n              <div className=\"text-blue-100\">总项目数</div>\n            </div>\n            <div className=\"animate-fade-in\">\n              <div className=\"text-4xl md:text-5xl font-bold mb-2\">{settings.active_members || 35}+</div>\n              <div className=\"text-blue-100\">活跃成员</div>\n            </div>\n            <div className=\"animate-fade-in\">\n              <div className=\"text-4xl md:text-5xl font-bold mb-2\">{settings.completed_projects || 95}+</div>\n              <div className=\"text-blue-100\">完成项目</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 教室功能区域 */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              创客教室功能区域\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              专业设计的多功能学习空间，满足不同类型的创客活动需求\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\">\n                <Lightbulb className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">展示区</h3>\n              <p className=\"text-gray-600\">\n                用于展示学生作品、项目成果和优秀案例，激发学习兴趣和创作灵感\n              </p>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4\">\n                <Users className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">讨论区</h3>\n              <p className=\"text-gray-600\">\n                学生进行头脑风暴、团队协作和项目讨论的开放式学习空间\n              </p>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4\">\n                <BookOpen className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">学习区</h3>\n              <p className=\"text-gray-600\">\n                学生进行理论学习和课程学习的专门区域，配备现代化教学设备\n              </p>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4\">\n                <Wrench className=\"h-6 w-6 text-orange-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">制作区</h3>\n              <p className=\"text-gray-600\">\n                小组制作和个人创作空间，提供各种工具和材料支持实践活动\n              </p>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4\">\n                <Printer className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">设备区</h3>\n              <p className=\"text-gray-600\">\n                配备3D打印机、激光切割机等先进设备，支持高精度制作需求\n              </p>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4\">\n                <Cpu className=\"h-6 w-6 text-indigo-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">编程区</h3>\n              <p className=\"text-gray-600\">\n                专门的编程学习和机器人制作区域，培养学生的计算思维能力\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 设施设备 */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              先进的设施设备\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              配备专业级设备，为学生提供最佳的学习和创作环境\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\">\n            <div>\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">主要设备清单</h3>\n              <div className=\"space-y-4\">\n                {mainEquipment.length > 0 ? (\n                  mainEquipment.map((equipment, index) => {\n                    const colors = ['blue', 'green', 'purple', 'orange', 'red', 'indigo', 'pink', 'yellow']\n                    const color = colors[index % colors.length]\n                    return (\n                      <div key={equipment.id} className=\"flex items-start\">\n                        <div className={`w-2 h-2 bg-${color}-600 rounded-full mt-2 mr-3`}></div>\n                        <div>\n                          <h4 className=\"font-semibold text-gray-900\">\n                            {equipment.name} {equipment.quantity > 1 && `(${equipment.quantity}台)`}\n                          </h4>\n                          <p className=\"text-gray-600 text-sm\">{equipment.description}</p>\n                        </div>\n                      </div>\n                    )\n                  })\n                ) : (\n                  <>\n                    <div className=\"flex items-start\">\n                      <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">3D打印设备</h4>\n                        <p className=\"text-gray-600 text-sm\">高精度3D打印机，支持PLA、ABS等多种材料</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start\">\n                      <div className=\"w-2 h-2 bg-green-600 rounded-full mt-2 mr-3\"></div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">编程学习平台</h4>\n                        <p className=\"text-gray-600 text-sm\">图形化编程软件和硬件开发板</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start\">\n                      <div className=\"w-2 h-2 bg-purple-600 rounded-full mt-2 mr-3\"></div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">机器人套件</h4>\n                        <p className=\"text-gray-600 text-sm\">多种类型的机器人制作和编程套件</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start\">\n                      <div className=\"w-2 h-2 bg-orange-600 rounded-full mt-2 mr-3\"></div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">电子制作工具</h4>\n                        <p className=\"text-gray-600 text-sm\">电烙铁、万用表、示波器等电子制作工具</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start\">\n                      <div className=\"w-2 h-2 bg-red-600 rounded-full mt-2 mr-3\"></div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">多媒体设备</h4>\n                        <p className=\"text-gray-600 text-sm\">投影仪、交互式白板、平板电脑等</p>\n                      </div>\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n\n            <div className=\"bg-gray-100 p-8 rounded-lg\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">教室规格</h3>\n              <div className=\"space-y-3\">\n                {classroomSpecs.length > 0 ? (\n                  classroomSpecs.map((spec) => (\n                    <div key={spec.id} className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">{spec.name}：</span>\n                      <span className=\"font-semibold\">{spec.value}{spec.unit}</span>\n                    </div>\n                  ))\n                ) : (\n                  <>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">使用面积：</span>\n                      <span className=\"font-semibold\">80平方米以上</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">容纳人数：</span>\n                      <span className=\"font-semibold\">30人同时上课</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">工作台数：</span>\n                      <span className=\"font-semibold\">16个学生实验台</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">设备台数：</span>\n                      <span className=\"font-semibold\">16台3D打印机</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">展示空间：</span>\n                      <span className=\"font-semibold\">专门的作品展示区</span>\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 课程体系 */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              创新课程体系\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              系统化的STEAM课程设计，培养学生的综合能力\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">基础课程</h3>\n              <ul className=\"space-y-2 text-gray-600\">\n                <li>• 3D建模与设计</li>\n                <li>• 图形化编程入门</li>\n                <li>• 电子电路基础</li>\n                <li>• 机械结构原理</li>\n                <li>• 创意思维训练</li>\n              </ul>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">进阶课程</h3>\n              <ul className=\"space-y-2 text-gray-600\">\n                <li>• 机器人制作与编程</li>\n                <li>• 智能硬件开发</li>\n                <li>• 传感器应用</li>\n                <li>• 物联网项目</li>\n                <li>• 人工智能入门</li>\n              </ul>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">项目课程</h3>\n              <ul className=\"space-y-2 text-gray-600\">\n                <li>• 创新发明项目</li>\n                <li>• 科技竞赛准备</li>\n                <li>• 跨学科综合项目</li>\n                <li>• 社会问题解决方案</li>\n                <li>• 创业项目孵化</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4E,EAAE;IAC/H,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkE,EAAE;IAEvH,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,IAAI,SAAS,cAAc,EAAE;YAC3B,IAAI;gBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,SAAS,cAAc;gBACxD,iBAAiB;YACnB,EAAE,OAAO,GAAG;gBACV,iBAAiB,EAAE;YACrB;QACF;QAEA,WAAW;QACX,IAAI,SAAS,eAAe,EAAE;YAC5B,IAAI;gBACF,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,eAAe;gBACrD,kBAAkB;YACpB,EAAE,OAAO,GAAG;gBACV,kBAAkB,EAAE;YACtB;QACF;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;0BAQlE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAuC,SAAS,aAAa,IAAI;4CAAG;;;;;;;kDACnF,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAuC,SAAS,cAAc,IAAI;4CAAI;;;;;;;kDACrF,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAuC,SAAS,cAAc,IAAI;4CAAG;;;;;;;kDACpF,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAuC,SAAS,kBAAkB,IAAI;4CAAG;;;;;;;kDACxF,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,WAAW;gDAC5B,MAAM,SAAS;oDAAC;oDAAQ;oDAAS;oDAAU;oDAAU;oDAAO;oDAAU;oDAAQ;iDAAS;gDACvF,MAAM,QAAQ,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;gDAC3C,qBACE,8OAAC;oDAAuB,WAAU;;sEAChC,8OAAC;4DAAI,WAAW,CAAC,WAAW,EAAE,MAAM,2BAA2B,CAAC;;;;;;sEAChE,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;;wEACX,UAAU,IAAI;wEAAC;wEAAE,UAAU,QAAQ,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,QAAQ,CAAC,EAAE,CAAC;;;;;;;8EAExE,8OAAC;oEAAE,WAAU;8EAAyB,UAAU,WAAW;;;;;;;;;;;;;mDANrD,UAAU,EAAE;;;;;4CAU1B,mBAEA;;kEACE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDACZ,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,qBAClB,8OAAC;oDAAkB,WAAU;;sEAC3B,8OAAC;4DAAK,WAAU;;gEAAiB,KAAK,IAAI;gEAAC;;;;;;;sEAC3C,8OAAC;4DAAK,WAAU;;gEAAiB,KAAK,KAAK;gEAAE,KAAK,IAAI;;;;;;;;mDAF9C,KAAK,EAAE;;;;0EAMnB;;kEACE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}]}
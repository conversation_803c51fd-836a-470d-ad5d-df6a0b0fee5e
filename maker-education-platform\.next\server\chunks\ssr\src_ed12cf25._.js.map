{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/layout/admin-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport {\n  LayoutDashboard,\n  Users,\n  FileText,\n  UserCheck,\n  Settings,\n  BarChart3,\n  Menu,\n  X,\n  LogOut,\n  Home,\n  Clock,\n  Plus,\n  Activity\n} from 'lucide-react'\n\nconst sidebarItems = [\n  { name: '仪表板', href: '/admin', icon: LayoutDashboard },\n  { name: '用户管理', href: '/admin/users', icon: Users },\n  { name: '作品管理', href: '/admin/projects', icon: FileText },\n  { name: '团队管理', href: '/admin/team-members', icon: UserCheck },\n  { name: '申请审批', href: '/admin/applications', icon: Clock },\n  { name: '排班管理', href: '/admin/schedule', icon: Clock },\n  { name: '博客管理', href: '/admin/posts', icon: FileText },\n  { name: '数据分析', href: '/admin/analytics', icon: BarChart3 },\n  { name: '系统监控', href: '/admin/system', icon: Activity },\n  { name: '系统设置', href: '/admin/settings', icon: Settings },\n]\n\ninterface AdminLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AdminLayout({ children }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { data: session } = useSession()\n\n  return (\n    <div className=\"admin-layout min-h-screen bg-gray-50\">\n      {/* Mobile sidebar backdrop */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        admin-sidebar fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-enhanced transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col lg:h-screen\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-white flex-shrink-0\">\n          <Link href=\"/admin\" className=\"flex items-center\">\n            <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\n              <span className=\"text-xl font-bold\">管理后台</span>\n            </div>\n          </Link>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-colors\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        <nav className=\"flex-1 mt-6 px-3 overflow-y-auto\">\n          <div className=\"space-y-1\">\n            {sidebarItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n              \n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200\n                    ${isActive \n                      ? 'bg-blue-50 text-blue-700 shadow-sm' \n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                    }\n                  `}\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <Icon className={`\n                    mr-3 h-5 w-5 transition-colors\n                    ${isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}\n                  `} />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </div>\n\n          <div className=\"mt-8 pt-6 border-t border-gray-200\">\n            <div className=\"space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"group flex items-center px-3 py-3 text-sm font-medium text-gray-700 rounded-xl hover:bg-gray-50 hover:text-gray-900 transition-all duration-200\"\n              >\n                <Home className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                返回前台\n              </Link>\n              <button\n                onClick={() => signOut()}\n                className=\"w-full group flex items-center px-3 py-3 text-sm font-medium text-gray-700 rounded-xl hover:bg-gray-50 hover:text-gray-900 transition-all duration-200\"\n              >\n                <LogOut className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\" />\n                退出登录\n              </button>\n            </div>\n          </div>\n        </nav>\n      </div>\n\n      {/* Main content */}\n      <div className=\"admin-main flex-1 flex flex-col min-h-screen\">\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200 px-4 sm:px-6 lg:px-8 flex-shrink-0 sticky top-0 z-30\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-colors\"\n              >\n                <Menu className=\"w-5 h-5\" />\n              </button>\n              <h1 className=\"ml-2 lg:ml-0 text-2xl font-semibold text-gray-900\">\n                {sidebarItems.find(item => item.href === pathname)?.name || '管理后台'}\n              </h1>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"hidden sm:flex items-center text-sm text-gray-500\">\n                欢迎, {session?.user?.name}\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-medium\">\n                  {session?.user?.name?.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 p-4 sm:p-6 lg:p-8 overflow-auto\">\n          <div className=\"animate-fade-in max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAuBA,MAAM,eAAe;IACnB;QAAE,MAAM;QAAO,MAAM;QAAU,MAAM,4NAAA,CAAA,kBAAe;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,gNAAA,CAAA,YAAS;IAAC;IAC7D;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACzD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAgB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAoB,MAAM,kNAAA,CAAA,YAAS;IAAC;IAC1D;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACzD;AAMc,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,8OAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,cAAc,kBAAkB,oBAAoB;MACxD,CAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAC5B,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;;;;;0CAGxC,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC;oCACjB,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC;;oBAEV,EAAE,WACE,uCACA,qDACH;kBACH,CAAC;wCACD,SAAS,IAAM,eAAe;;0DAE9B,8OAAC;gDAAK,WAAW,CAAC;;oBAEhB,EAAE,WAAW,kBAAkB,0CAA0C;kBAC3E,CAAC;;;;;;4CACA,KAAK,IAAI;;uCAfL,KAAK,IAAI;;;;;gCAkBpB;;;;;;0CAGF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAyD;;;;;;;sDAG3E,8OAAC;4CACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4CACrB,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDACX,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,QAAQ;;;;;;;;;;;;8CAIhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAoD;gDAC5D,SAAS,MAAM;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,SAAS,MAAM,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/admin/settings/advanced/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport AdminLayout from '@/components/layout/admin-layout'\nimport { Button } from '@/components/ui/button'\nimport { ArrowLeft, Save, Palette, Eye, Globe, BarChart3, Settings2 } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function AdvancedSettingsPage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [settings, setSettings] = useState<Record<string, any>>({\n    // 外观设置\n    primary_color: '#3B82F6',\n    secondary_color: '#6366F1',\n    hero_background: 'gradient',\n    show_animations: true,\n    dark_mode_enabled: false,\n    \n    // 功能开关\n    show_team_section: true,\n    show_projects_section: true,\n    show_blog_section: true,\n    show_apply_section: true,\n    enable_comments: true,\n    enable_likes: true,\n    \n    // 首页内容\n    hero_title: '创新思维 · 实践能力',\n    hero_subtitle: 'STEAM创客教育平台',\n    hero_description: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',\n    about_intro: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',\n    \n    // 统计数据\n    stats_projects: 500,\n    stats_students: 1000,\n    stats_satisfaction: 98,\n    \n    // SEO设置\n    meta_keywords: 'STEAM教育,创客教育,3D打印,机器人,编程',\n    meta_description: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程',\n  })\n  const [isLoading, setIsLoading] = useState(false)\n  const [message, setMessage] = useState('')\n\n  useEffect(() => {\n    if (status === 'loading') return\n    if (!session || session.user.role !== 'ADMIN') {\n      router.push('/')\n      return\n    }\n    fetchSettings()\n  }, [session, status, router])\n\n  const fetchSettings = async () => {\n    try {\n      const response = await fetch('/api/admin/settings')\n      if (response.ok) {\n        const data = await response.json()\n        const formattedSettings = Object.keys(data).reduce((acc, key) => {\n          if (settings.hasOwnProperty(key)) {\n            acc[key] = data[key].value\n          }\n          return acc\n        }, {} as any)\n        setSettings(prev => ({ ...prev, ...formattedSettings }))\n      }\n    } catch (error) {\n      console.error('Failed to fetch settings:', error)\n    }\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target\n    setSettings(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : \n              type === 'number' ? parseFloat(value) || 0 : value\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setMessage('')\n\n    try {\n      const formattedSettings = Object.keys(settings).reduce((acc, key) => {\n        const value = settings[key as keyof typeof settings]\n        let type = 'STRING'\n        \n        if (typeof value === 'boolean') {\n          type = 'BOOLEAN'\n        } else if (typeof value === 'number') {\n          type = 'NUMBER'\n        }\n\n        acc[key] = {\n          value,\n          type,\n          category: getCategoryForKey(key)\n        }\n        return acc\n      }, {} as any)\n\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(formattedSettings)\n      })\n\n      if (response.ok) {\n        setMessage('高级设置保存成功！')\n      } else {\n        setMessage('保存失败，请重试')\n      }\n      setTimeout(() => setMessage(''), 3000)\n    } catch (error) {\n      setMessage('保存失败，请重试')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getCategoryForKey = (key: string) => {\n    if (['primary_color', 'secondary_color', 'hero_background', 'show_animations', 'dark_mode_enabled'].includes(key)) return 'APPEARANCE'\n    if (['show_team_section', 'show_projects_section', 'show_blog_section', 'show_apply_section', 'enable_comments', 'enable_likes'].includes(key)) return 'FEATURES'\n    if (['hero_title', 'hero_subtitle', 'hero_description', 'about_intro'].includes(key)) return 'CONTENT'\n    if (['stats_projects', 'stats_students', 'stats_satisfaction'].includes(key)) return 'STATS'\n    if (['meta_keywords', 'meta_description'].includes(key)) return 'SEO'\n    return 'GENERAL'\n  }\n\n  if (status === 'loading') {\n    return (\n      <AdminLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"loading-spinner w-8 h-8\"></div>\n        </div>\n      </AdminLayout>\n    )\n  }\n\n  if (!session || session.user.role !== 'ADMIN') {\n    return null\n  }\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/admin/settings\">\n              <Button variant=\"outline\" size=\"sm\" className=\"rounded-xl\">\n                <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                返回基本设置\n              </Button>\n            </Link>\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900\">高级设置</h2>\n              <p className=\"text-gray-600\">自定义网站外观、功能和内容</p>\n            </div>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* 外观设置 */}\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <Palette className=\"w-5 h-5 mr-2\" />\n              外观设置\n            </h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  主题色\n                </label>\n                <input\n                  type=\"color\"\n                  name=\"primary_color\"\n                  value={settings.primary_color}\n                  onChange={handleChange}\n                  className=\"w-full h-10 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  辅助色\n                </label>\n                <input\n                  type=\"color\"\n                  name=\"secondary_color\"\n                  value={settings.secondary_color}\n                  onChange={handleChange}\n                  className=\"w-full h-10 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  首页背景样式\n                </label>\n                <select\n                  name=\"hero_background\"\n                  value={settings.hero_background}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"gradient\">渐变背景</option>\n                  <option value=\"solid\">纯色背景</option>\n                  <option value=\"image\">图片背景</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"mt-6 space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">显示动画效果</label>\n                  <p className=\"text-sm text-gray-500\">启用页面动画和过渡效果</p>\n                </div>\n                <input\n                  type=\"checkbox\"\n                  name=\"show_animations\"\n                  checked={settings.show_animations}\n                  onChange={handleChange}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">启用暗色模式</label>\n                  <p className=\"text-sm text-gray-500\">提供暗色主题选项</p>\n                </div>\n                <input\n                  type=\"checkbox\"\n                  name=\"dark_mode_enabled\"\n                  checked={settings.dark_mode_enabled}\n                  onChange={handleChange}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* 功能开关 */}\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <Settings2 className=\"w-5 h-5 mr-2\" />\n              功能开关\n            </h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700\">显示团队介绍</label>\n                    <p className=\"text-sm text-gray-500\">在导航栏显示团队页面</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    name=\"show_team_section\"\n                    checked={settings.show_team_section}\n                    onChange={handleChange}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700\">显示作品展示</label>\n                    <p className=\"text-sm text-gray-500\">在导航栏显示作品页面</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    name=\"show_projects_section\"\n                    checked={settings.show_projects_section}\n                    onChange={handleChange}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700\">显示博客功能</label>\n                    <p className=\"text-sm text-gray-500\">启用博客文章功能</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    name=\"show_blog_section\"\n                    checked={settings.show_blog_section}\n                    onChange={handleChange}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700\">显示申请功能</label>\n                    <p className=\"text-sm text-gray-500\">允许用户提交申请</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    name=\"show_apply_section\"\n                    checked={settings.show_apply_section}\n                    onChange={handleChange}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700\">启用评论功能</label>\n                    <p className=\"text-sm text-gray-500\">允许用户发表评论</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    name=\"enable_comments\"\n                    checked={settings.enable_comments}\n                    onChange={handleChange}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700\">启用点赞功能</label>\n                    <p className=\"text-sm text-gray-500\">允许用户点赞内容</p>\n                  </div>\n                  <input\n                    type=\"checkbox\"\n                    name=\"enable_likes\"\n                    checked={settings.enable_likes}\n                    onChange={handleChange}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* 首页内容设置 */}\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <Globe className=\"w-5 h-5 mr-2\" />\n              首页内容\n            </h3>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  首页主标题\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"hero_title\"\n                  value={settings.hero_title}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  首页副标题\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"hero_subtitle\"\n                  value={settings.hero_subtitle}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  首页描述\n                </label>\n                <textarea\n                  name=\"hero_description\"\n                  value={settings.hero_description}\n                  onChange={handleChange}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  关于我们简介\n                </label>\n                <textarea\n                  name=\"about_intro\"\n                  value={settings.about_intro}\n                  onChange={handleChange}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* 统计数据设置 */}\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <BarChart3 className=\"w-5 h-5 mr-2\" />\n              统计数据\n            </h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  学生作品数\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"stats_projects\"\n                  value={settings.stats_projects}\n                  onChange={handleChange}\n                  min=\"0\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  培训学员数\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"stats_students\"\n                  value={settings.stats_students}\n                  onChange={handleChange}\n                  min=\"0\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  满意度评价 (%)\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"stats_satisfaction\"\n                  value={settings.stats_satisfaction}\n                  onChange={handleChange}\n                  min=\"0\"\n                  max=\"100\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* SEO设置 */}\n          <div className=\"bg-white rounded-xl shadow-enhanced p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <Eye className=\"w-5 h-5 mr-2\" />\n              SEO设置\n            </h3>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  SEO关键词\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"meta_keywords\"\n                  value={settings.meta_keywords}\n                  onChange={handleChange}\n                  placeholder=\"用逗号分隔多个关键词\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  SEO描述\n                </label>\n                <textarea\n                  name=\"meta_description\"\n                  value={settings.meta_description}\n                  onChange={handleChange}\n                  rows={3}\n                  placeholder=\"网站的SEO描述，建议150字以内\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* 保存按钮 */}\n          <div className=\"flex justify-end\">\n            <Button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"btn-enhanced btn-submit rounded-xl px-8\"\n            >\n              <Save className=\"w-4 h-4 mr-2\" />\n              {isLoading ? '保存中...' : '保存高级设置'}\n            </Button>\n          </div>\n\n          {/* 消息提示 */}\n          {message && (\n            <div className={`p-4 rounded-xl ${\n              message.includes('成功') \n                ? 'bg-green-50 text-green-800 border border-green-200' \n                : 'bg-red-50 text-red-800 border border-red-200'\n            }`}>\n              {message}\n            </div>\n          )}\n        </form>\n      </div>\n    </AdminLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QAC5D,OAAO;QACP,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;QAEnB,OAAO;QACP,mBAAmB;QACnB,uBAAuB;QACvB,mBAAmB;QACnB,oBAAoB;QACpB,iBAAiB;QACjB,cAAc;QAEd,OAAO;QACP,YAAY;QACZ,eAAe;QACf,kBAAkB;QAClB,aAAa;QAEb,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,oBAAoB;QAEpB,QAAQ;QACR,eAAe;QACf,kBAAkB;IACpB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;QAC1B,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,IAAI,CAAC;YACZ;QACF;QACA;IACF,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,oBAAoB,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC,CAAC,KAAK;oBACvD,IAAI,SAAS,cAAc,CAAC,MAAM;wBAChC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;oBAC5B;oBACA,OAAO;gBACT,GAAG,CAAC;gBACJ,YAAY,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,GAAG,iBAAiB;oBAAC,CAAC;YACxD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAC5D,SAAS,WAAW,WAAW,UAAU,IAAI;YACvD,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,WAAW;QAEX,IAAI;YACF,MAAM,oBAAoB,OAAO,IAAI,CAAC,UAAU,MAAM,CAAC,CAAC,KAAK;gBAC3D,MAAM,QAAQ,QAAQ,CAAC,IAA6B;gBACpD,IAAI,OAAO;gBAEX,IAAI,OAAO,UAAU,WAAW;oBAC9B,OAAO;gBACT,OAAO,IAAI,OAAO,UAAU,UAAU;oBACpC,OAAO;gBACT;gBAEA,GAAG,CAAC,IAAI,GAAG;oBACT;oBACA;oBACA,UAAU,kBAAkB;gBAC9B;gBACA,OAAO;YACT,GAAG,CAAC;YAEJ,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;YACb,OAAO;gBACL,WAAW;YACb;YACA,WAAW,IAAM,WAAW,KAAK;QACnC,EAAE,OAAO,OAAO;YACd,WAAW;QACb,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI;YAAC;YAAiB;YAAmB;YAAmB;YAAmB;SAAoB,CAAC,QAAQ,CAAC,MAAM,OAAO;QAC1H,IAAI;YAAC;YAAqB;YAAyB;YAAqB;YAAsB;YAAmB;SAAe,CAAC,QAAQ,CAAC,MAAM,OAAO;QACvJ,IAAI;YAAC;YAAc;YAAiB;YAAoB;SAAc,CAAC,QAAQ,CAAC,MAAM,OAAO;QAC7F,IAAI;YAAC;YAAkB;YAAkB;SAAqB,CAAC,QAAQ,CAAC,MAAM,OAAO;QACrF,IAAI;YAAC;YAAiB;SAAmB,CAAC,QAAQ,CAAC,MAAM,OAAO;QAChE,OAAO;IACT;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC,+IAAA,CAAA,UAAW;sBACV,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QAC7C,OAAO;IACT;IAEA,qBACE,8OAAC,+IAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI1C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAKnC,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAItC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,aAAa;oDAC7B,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,eAAe;oDAC/B,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,eAAe;oDAC/B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAW;;;;;;sEACzB,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;8CAK5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAoC;;;;;;sEACrD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,SAAS,SAAS,eAAe;oDACjC,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAoC;;;;;;sEACrD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,SAAS,SAAS,iBAAiB;oDACnC,UAAU;oDACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAoC;;;;;;8EACrD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS,SAAS,iBAAiB;4DACnC,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAoC;;;;;;8EACrD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS,SAAS,qBAAqB;4DACvC,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAoC;;;;;;8EACrD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS,SAAS,iBAAiB;4DACnC,UAAU;4DACV,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAoC;;;;;;8EACrD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS,SAAS,kBAAkB;4DACpC,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAoC;;;;;;8EACrD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS,SAAS,eAAe;4DACjC,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAoC;;;;;;8EACrD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS,SAAS,YAAY;4DAC9B,UAAU;4DACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,aAAa;oDAC7B,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,gBAAgB;oDAChC,UAAU;oDACV,MAAM;oDACN,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,cAAc;oDAC9B,UAAU;oDACV,KAAI;oDACJ,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,cAAc;oDAC9B,UAAU;oDACV,KAAI;oDACJ,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,kBAAkB;oDAClC,UAAU;oDACV,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,aAAa;oDAC7B,UAAU;oDACV,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,gBAAgB;oDAChC,UAAU;oDACV,MAAM;oDACN,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU;gCACV,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,YAAY,WAAW;;;;;;;;;;;;wBAK3B,yBACC,8OAAC;4BAAI,WAAW,CAAC,eAAe,EAC9B,QAAQ,QAAQ,CAAC,QACb,uDACA,gDACJ;sCACC;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}
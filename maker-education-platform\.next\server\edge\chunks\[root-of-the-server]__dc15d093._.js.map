{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware'\nimport { NextResponse } from 'next/server'\n\nexport default withAuth(\n  async function middleware(req) {\n    const { pathname } = req.nextUrl\n    const token = req.nextauth.token\n\n    // 检查是否访问管理员路径\n    if (pathname.startsWith('/admin')) {\n      // 如果没有登录，重定向到登录页面\n      if (!token) {\n        const loginUrl = new URL('/auth/signin', req.url)\n        loginUrl.searchParams.set('callbackUrl', pathname)\n        return NextResponse.redirect(loginUrl)\n      }\n\n      // 如果不是管理员，返回403\n      if (token.role !== 'ADMIN') {\n        return new NextResponse('Forbidden', { status: 403 })\n      }\n    }\n\n    return NextResponse.next()\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        const { pathname } = req.nextUrl\n\n        // 对于管理员路径，需要验证用户角色\n        if (pathname.startsWith('/admin')) {\n          return token?.role === 'ADMIN'\n        }\n\n        // 对于其他路径，允许访问\n        return true\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: [\n    '/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)',\n  ]\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,eAAe,WAAW,GAAG;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAChC,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK;IAEhC,cAAc;IACd,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,kBAAkB;QAClB,IAAI,CAAC,OAAO;YACV,MAAM,WAAW,IAAI,IAAI,gBAAgB,IAAI,GAAG;YAChD,SAAS,YAAY,CAAC,GAAG,CAAC,eAAe;YACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,gBAAgB;QAChB,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,aAAa;gBAAE,QAAQ;YAAI;QACrD;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;YAEhC,mBAAmB;YACnB,IAAI,SAAS,UAAU,CAAC,WAAW;gBACjC,OAAO,OAAO,SAAS;YACzB;YAEA,cAAc;YACd,OAAO;QACT;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware'\nimport { NextResponse } from 'next/server'\n\n// 检查维护模式的函数\nasync function checkMaintenanceMode() {\n  try {\n    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/settings/public`, {\n      cache: 'no-store'\n    })\n    if (response.ok) {\n      const settings = await response.json()\n      return settings.maintenance_mode === true\n    }\n  } catch (error) {\n    console.error('Failed to check maintenance mode:', error)\n  }\n  return false\n}\n\nexport default withAuth(\n  async function middleware(req) {\n    const { pathname } = req.nextUrl\n    const token = req.nextauth.token\n\n    // 检查维护模式\n    const isMaintenanceMode = await checkMaintenanceMode()\n\n    // 如果是维护模式，且不是管理员，且不是维护页面或API路径\n    if (isMaintenanceMode &&\n        !pathname.startsWith('/admin') &&\n        !pathname.startsWith('/api') &&\n        !pathname.startsWith('/auth') &&\n        pathname !== '/maintenance' &&\n        token?.role !== 'ADMIN') {\n      return NextResponse.redirect(new URL('/maintenance', req.url))\n    }\n\n    // 如果访问维护页面但不是维护模式，重定向到首页\n    if (pathname === '/maintenance' && !isMaintenanceMode) {\n      return NextResponse.redirect(new URL('/', req.url))\n    }\n\n    // 检查是否访问管理员路径\n    if (pathname.startsWith('/admin')) {\n      // 如果没有登录，重定向到登录页面\n      if (!token) {\n        const loginUrl = new URL('/auth/signin', req.url)\n        loginUrl.searchParams.set('callbackUrl', pathname)\n        return NextResponse.redirect(loginUrl)\n      }\n\n      // 如果不是管理员，返回403\n      if (token.role !== 'ADMIN') {\n        return new NextResponse('Forbidden', { status: 403 })\n      }\n    }\n\n    return NextResponse.next()\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        const { pathname } = req.nextUrl\n\n        // 对于管理员路径，需要验证用户角色\n        if (pathname.startsWith('/admin')) {\n          return token?.role === 'ADMIN'\n        }\n\n        // 对于其他路径，允许访问\n        return true\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: [\n    '/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)',\n  ]\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,YAAY;AACZ,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,EAAE;YAC9E,OAAO;QACT;QACA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,WAAW,MAAM,SAAS,IAAI;YACpC,OAAO,SAAS,gBAAgB,KAAK;QACvC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;IACrD;IACA,OAAO;AACT;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,eAAe,WAAW,GAAG;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAChC,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK;IAEhC,SAAS;IACT,MAAM,oBAAoB,MAAM;IAEhC,+BAA+B;IAC/B,IAAI,qBACA,CAAC,SAAS,UAAU,CAAC,aACrB,CAAC,SAAS,UAAU,CAAC,WACrB,CAAC,SAAS,UAAU,CAAC,YACrB,aAAa,kBACb,OAAO,SAAS,SAAS;QAC3B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,IAAI,GAAG;IAC9D;IAEA,yBAAyB;IACzB,IAAI,aAAa,kBAAkB,CAAC,mBAAmB;QACrD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,IAAI,GAAG;IACnD;IAEA,cAAc;IACd,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,kBAAkB;QAClB,IAAI,CAAC,OAAO;YACV,MAAM,WAAW,IAAI,IAAI,gBAAgB,IAAI,GAAG;YAChD,SAAS,YAAY,CAAC,GAAG,CAAC,eAAe;YACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,gBAAgB;QAChB,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,aAAa;gBAAE,QAAQ;YAAI;QACrD;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;YAEhC,mBAAmB;YACnB,IAAI,SAAS,UAAU,CAAC,WAAW;gBACjC,OAAO,OAAO,SAAS;YACzB;YAEA,cAAc;YACd,OAAO;QACT;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}
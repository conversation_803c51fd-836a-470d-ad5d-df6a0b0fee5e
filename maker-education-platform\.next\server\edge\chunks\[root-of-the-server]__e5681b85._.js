(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__e5681b85._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ensureDbConnection": (()=>ensureDbConnection),
    "prisma": (()=>prisma),
    "safeDbOperation": (()=>safeDbOperation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$prisma$2f$client$2f$default$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@prisma/client/default.js [middleware-edge] (ecmascript)");
;
const globalForPrisma = globalThis;
// 创建带有重试机制的Prisma客户端
const createPrismaClient = ()=>{
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$prisma$2f$client$2f$default$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PrismaClient"]({
        log: ("TURBOPACK compile-time truthy", 1) ? [
            'error',
            'warn'
        ] : ("TURBOPACK unreachable", undefined)
    });
};
const prisma = globalForPrisma.prisma ?? createPrismaClient();
if ("TURBOPACK compile-time truthy", 1) {
    globalForPrisma.prisma = prisma;
}
async function ensureDbConnection() {
    try {
        await prisma.$connect();
        return true;
    } catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
}
async function safeDbOperation(operation, fallback) {
    try {
        await ensureDbConnection();
        return await operation();
    } catch (error) {
        console.error('Database operation failed:', error);
        return fallback;
    }
}
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/middleware.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
;
// 检查维护模式的函数
async function checkMaintenanceMode() {
    try {
        // 直接查询数据库而不是调用API，避免无限循环
        const { prisma } = await Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/lib/prisma.ts [middleware-edge] (ecmascript)"));
        const setting = await prisma.setting.findUnique({
            where: {
                key: 'maintenance_mode'
            }
        });
        return setting?.value === 'true';
    } catch (error) {
        console.error('Failed to check maintenance mode:', error);
    }
    return false;
}
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["withAuth"])(async function middleware(req) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;
    // 暂时禁用维护模式检查以避免循环调用
    // const isMaintenanceMode = await checkMaintenanceMode()
    // 如果是维护模式，且不是管理员，且不是维护页面或API路径
    // if (isMaintenanceMode &&
    //     !pathname.startsWith('/admin') &&
    //     !pathname.startsWith('/api') &&
    //     !pathname.startsWith('/auth') &&
    //     pathname !== '/maintenance' &&
    //     token?.role !== 'ADMIN') {
    //   return NextResponse.redirect(new URL('/maintenance', req.url))
    // }
    // 如果访问维护页面但不是维护模式，重定向到首页
    // if (pathname === '/maintenance' && !isMaintenanceMode) {
    //   return NextResponse.redirect(new URL('/', req.url))
    // }
    // 检查是否访问管理员路径
    if (pathname.startsWith('/admin')) {
        // 如果没有登录，重定向到登录页面
        if (!token) {
            const loginUrl = new URL('/auth/signin', req.url);
            loginUrl.searchParams.set('callbackUrl', pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
        }
        // 如果不是管理员，返回403
        if (token.role !== 'ADMIN') {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"]('Forbidden', {
                status: 403
            });
        }
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}, {
    callbacks: {
        authorized: ({ token, req })=>{
            const { pathname } = req.nextUrl;
            // 对于管理员路径，需要验证用户角色
            if (pathname.startsWith('/admin')) {
                return token?.role === 'ADMIN';
            }
            // 对于其他路径，允许访问
            return true;
        }
    }
});
const config = {
    matcher: [
        '/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__e5681b85._.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\n// 创建带有重试机制的Prisma客户端\nconst createPrismaClient = () => {\n  return new PrismaClient({\n    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],\n  })\n}\n\nexport const prisma = globalForPrisma.prisma ?? createPrismaClient()\n\nif (process.env.NODE_ENV !== 'production') {\n  globalForPrisma.prisma = prisma\n}\n\n// 确保数据库连接\nexport async function ensureDbConnection() {\n  try {\n    await prisma.$connect()\n    return true\n  } catch (error) {\n    console.error('Database connection failed:', error)\n    return false\n  }\n}\n\n// 安全的数据库操作包装器\nexport async function safeDbOperation<T>(operation: () => Promise<T>, fallback: T): Promise<T> {\n  try {\n    await ensureDbConnection()\n    return await operation()\n  } catch (error) {\n    console.error('Database operation failed:', error)\n    return fallback\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIxB,qBAAqB;AACrB,MAAM,qBAAqB;IACzB,OAAO,IAAI,qJAAA,CAAA,eAAY,CAAC;QACtB,KAAK,uCAAyC;YAAC;YAAS;SAAO;IACjE;AACF;AAEO,MAAM,SAAS,gBAAgB,MAAM,IAAI;AAEhD,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;AAC3B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,QAAQ;QACrB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,eAAe,gBAAmB,SAA2B,EAAE,QAAW;IAC/E,IAAI;QACF,MAAM;QACN,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF"}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware'\nimport { NextResponse } from 'next/server'\n\n// 检查维护模式的函数\nasync function checkMaintenanceMode() {\n  try {\n    // 直接查询数据库而不是调用API，避免无限循环\n    const { prisma } = await import('@/lib/prisma')\n    const setting = await prisma.setting.findUnique({\n      where: { key: 'maintenance_mode' }\n    })\n    return setting?.value === 'true'\n  } catch (error) {\n    console.error('Failed to check maintenance mode:', error)\n  }\n  return false\n}\n\nexport default withAuth(\n  async function middleware(req) {\n    const { pathname } = req.nextUrl\n    const token = req.nextauth.token\n\n    // 暂时禁用维护模式检查以避免循环调用\n    // const isMaintenanceMode = await checkMaintenanceMode()\n\n    // 如果是维护模式，且不是管理员，且不是维护页面或API路径\n    // if (isMaintenanceMode &&\n    //     !pathname.startsWith('/admin') &&\n    //     !pathname.startsWith('/api') &&\n    //     !pathname.startsWith('/auth') &&\n    //     pathname !== '/maintenance' &&\n    //     token?.role !== 'ADMIN') {\n    //   return NextResponse.redirect(new URL('/maintenance', req.url))\n    // }\n\n    // 如果访问维护页面但不是维护模式，重定向到首页\n    // if (pathname === '/maintenance' && !isMaintenanceMode) {\n    //   return NextResponse.redirect(new URL('/', req.url))\n    // }\n\n    // 检查是否访问管理员路径\n    if (pathname.startsWith('/admin')) {\n      // 如果没有登录，重定向到登录页面\n      if (!token) {\n        const loginUrl = new URL('/auth/signin', req.url)\n        loginUrl.searchParams.set('callbackUrl', pathname)\n        return NextResponse.redirect(loginUrl)\n      }\n\n      // 如果不是管理员，返回403\n      if (token.role !== 'ADMIN') {\n        return new NextResponse('Forbidden', { status: 403 })\n      }\n    }\n\n    return NextResponse.next()\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        const { pathname } = req.nextUrl\n\n        // 对于管理员路径，需要验证用户角色\n        if (pathname.startsWith('/admin')) {\n          return token?.role === 'ADMIN'\n        }\n\n        // 对于其他路径，允许访问\n        return true\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: [\n    '/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)',\n  ]\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,YAAY;AACZ,eAAe;IACb,IAAI;QACF,yBAAyB;QACzB,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,KAAK;YAAmB;QACnC;QACA,OAAO,SAAS,UAAU;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;IACrD;IACA,OAAO;AACT;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,eAAe,WAAW,GAAG;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAChC,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK;IAEhC,oBAAoB;IACpB,yDAAyD;IAEzD,+BAA+B;IAC/B,2BAA2B;IAC3B,wCAAwC;IACxC,sCAAsC;IACtC,uCAAuC;IACvC,qCAAqC;IACrC,iCAAiC;IACjC,mEAAmE;IACnE,IAAI;IAEJ,yBAAyB;IACzB,2DAA2D;IAC3D,wDAAwD;IACxD,IAAI;IAEJ,cAAc;IACd,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,kBAAkB;QAClB,IAAI,CAAC,OAAO;YACV,MAAM,WAAW,IAAI,IAAI,gBAAgB,IAAI,GAAG;YAChD,SAAS,YAAY,CAAC,GAAG,CAAC,eAAe;YACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,gBAAgB;QAChB,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,aAAa;gBAAE,QAAQ;YAAI;QACrD;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;YAEhC,mBAAmB;YACnB,IAAI,SAAS,UAAU,CAAC,WAAW;gBACjC,OAAO,OAAO,SAAS;YACzB;YAEA,cAAc;YACd,OAAO;QACT;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}
{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xNL3o0npBCmFZezFUnqNiwNZ7BbEADEDiIpkumAShzY=", "__NEXT_PREVIEW_MODE_ID": "bd4a94db2cf9fc5f18fa626085fc3463", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "54441fb85c65b9018a3ed33b1ee62a88578c201ac15fc896f73bc23c8b513cbf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "72de34663b5321839e06908718916c09268af9a8d9a60d54af8ac0157a4c991c"}}}, "sortedMiddleware": ["/"], "functions": {}}
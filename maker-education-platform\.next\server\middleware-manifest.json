{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|api\\/auth).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xNL3o0npBCmFZezFUnqNiwNZ7BbEADEDiIpkumAShzY=", "__NEXT_PREVIEW_MODE_ID": "af893ba977f3a6131a3b2ce4407bf606", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "708e037c68262fd3e4e960264bf33f7868edb6a1de99505957929a5d5c44696a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "50162186e37f175172d4c87bba764d3682acf89a9bd1b87ad457bbedc4cca7ea"}}}, "sortedMiddleware": ["/"], "functions": {}}
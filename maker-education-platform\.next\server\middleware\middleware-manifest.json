{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|images|api/auth).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xNL3o0npBCmFZezFUnqNiwNZ7BbEADEDiIpkumAShzY=", "__NEXT_PREVIEW_MODE_ID": "f14c39ec2d349730cff411ec2b0a28fc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84d1cbee730780a65c3b443b792a5a726497e68e50415437ba4723226d1409d8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bdba9c2f54f044dc616bccb9b520c49df31e0fc87151b4c87a33569911f4a0e4"}}}, "instrumentation": null, "functions": {}}
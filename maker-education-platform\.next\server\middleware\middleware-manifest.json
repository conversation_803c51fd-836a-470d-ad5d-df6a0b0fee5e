{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|images|api/auth).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xNL3o0npBCmFZezFUnqNiwNZ7BbEADEDiIpkumAShzY=", "__NEXT_PREVIEW_MODE_ID": "5ebe3d343f4e8ce53fdb6ab7c537d1ed", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "63a0dbe0dd2a6aadcd0ed546e7d5b334fa2c16dd4b3f02efe5b9f241ab3dc842", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8c2c30f1f8b37c82441673f0ecc86aa1d7fa4eb91b8c5349a38220399b2d0c4f"}}}, "instrumentation": null, "functions": {}}
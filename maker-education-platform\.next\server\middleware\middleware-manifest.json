{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|images|api/auth).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xNL3o0npBCmFZezFUnqNiwNZ7BbEADEDiIpkumAShzY=", "__NEXT_PREVIEW_MODE_ID": "c31f7c85d1f09b39247d2aadb9b511c8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d3a30a77ceeb7a0a75ed5e07060048731486339a7f620af7c0380ef180a2e1ff", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7b8cc7a51fc3aa05db37c423f38676acbd1d1fe16462fe39078e83e5476e68b0"}}}, "instrumentation": null, "functions": {}}
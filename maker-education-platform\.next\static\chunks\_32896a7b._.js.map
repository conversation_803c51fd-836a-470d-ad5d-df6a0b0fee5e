{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/app/terms/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { ArrowLeft } from 'lucide-react'\nimport Link from 'next/link'\nimport { Metadata } from 'next'\n\nexport default function TermsPage() {\n  const [settings, setSettings] = useState<any>({})\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    fetchSettings()\n  }, [])\n\n  const fetchSettings = async () => {\n    try {\n      const response = await fetch('/api/settings/public')\n      if (response.ok) {\n        const data = await response.json()\n        setSettings(data)\n      }\n    } catch (error) {\n      console.error('Failed to fetch settings:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-12\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">加载中...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            返回首页\n          </Link>\n          <h1 className=\"text-3xl font-bold text-gray-900\">使用条款</h1>\n          <p className=\"text-gray-600 mt-2\">\n            最后更新：{settings.terms_last_updated ? new Date(settings.terms_last_updated).toLocaleDateString('zh-CN') : '2024年1月1日'}\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-lg p-8\">\n          <div className=\"prose prose-lg max-w-none\">\n            {settings.terms_content ? (\n              <div\n                className=\"whitespace-pre-wrap text-gray-700\"\n                dangerouslySetInnerHTML={{ __html: settings.terms_content.replace(/\\n/g, '<br>') }}\n              />\n            ) : (\n              <div>\n                <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">1. 接受条款</h2>\n              <p className=\"text-gray-700 mb-4\">\n                欢迎使用创客教育平台！通过访问或使用我们的服务，您同意受本使用条款的约束。\n                如果您不同意这些条款，请不要使用我们的服务。\n              </p>\n              <p className=\"text-gray-700\">\n                我们保留随时修改这些条款的权利。修改后的条款将在发布后立即生效。\n                继续使用服务即表示您接受修改后的条款。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">2. 服务描述</h2>\n              <p className=\"text-gray-700 mb-4\">\n                创客教育平台是一个专注于STEAM教育的在线平台，提供：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>创客教育课程和资源</li>\n                <li>学生作品展示平台</li>\n                <li>教育博客和文章</li>\n                <li>团队申请和管理功能</li>\n                <li>在线学习和交流社区</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">3. 用户账户</h2>\n              <p className=\"text-gray-700 mb-4\">\n                使用某些服务需要创建账户。您同意：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>提供准确、完整的注册信息</li>\n                <li>及时更新您的账户信息</li>\n                <li>保护您的账户密码安全</li>\n                <li>对您账户下的所有活动负责</li>\n                <li>立即通知我们任何未经授权的使用</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">4. 用户行为规范</h2>\n              <p className=\"text-gray-700 mb-4\">\n                使用我们的服务时，您不得：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>发布违法、有害、威胁、辱骂或诽谤性内容</li>\n                <li>侵犯他人的知识产权或隐私权</li>\n                <li>传播病毒、恶意软件或其他有害代码</li>\n                <li>进行垃圾邮件发送或其他形式的骚扰</li>\n                <li>尝试未经授权访问系统或其他用户账户</li>\n                <li>干扰或破坏服务的正常运行</li>\n                <li>使用自动化工具抓取网站内容</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">5. 内容和知识产权</h2>\n              <p className=\"text-gray-700 mb-4\">\n                <strong>我们的内容：</strong>\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2 mb-4\">\n                <li>平台上的所有内容（除用户生成内容外）均为我们所有</li>\n                <li>受版权、商标和其他知识产权法保护</li>\n                <li>未经许可不得复制、分发或修改</li>\n              </ul>\n              \n              <p className=\"text-gray-700 mb-4\">\n                <strong>用户内容：</strong>\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>您保留对自己创建内容的所有权</li>\n                <li>授予我们使用、展示、分发您内容的许可</li>\n                <li>确保您的内容不侵犯他人权利</li>\n                <li>我们有权删除违规内容</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">6. 隐私保护</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们重视您的隐私。我们的隐私政策详细说明了我们如何收集、使用和保护您的信息。\n                使用我们的服务即表示您同意我们的隐私政策。\n              </p>\n              <p className=\"text-gray-700\">\n                特别地，对于未成年用户，我们将严格遵守相关法律法规，保护未成年人的隐私和安全。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">7. 服务可用性</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们努力保持服务的可用性，但不保证：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>服务将不间断或无错误</li>\n                <li>所有功能在所有时间都可用</li>\n                <li>服务不会受到技术问题影响</li>\n              </ul>\n              <p className=\"text-gray-700 mt-4\">\n                我们保留随时修改、暂停或终止服务的权利，恕不另行通知。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">8. 免责声明</h2>\n              <p className=\"text-gray-700 mb-4\">\n                在法律允许的最大范围内：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>服务按\"现状\"提供，不提供任何明示或暗示的保证</li>\n                <li>我们不对服务的准确性、可靠性或完整性负责</li>\n                <li>我们不对因使用服务而产生的任何损失负责</li>\n                <li>我们不对第三方内容或链接负责</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">9. 责任限制</h2>\n              <p className=\"text-gray-700 mb-4\">\n                在任何情况下，我们对您的总责任不超过：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>您在过去12个月内支付给我们的费用总额</li>\n                <li>如果服务免费，则为人民币100元</li>\n              </ul>\n              <p className=\"text-gray-700 mt-4\">\n                我们不对间接、偶然、特殊或后果性损害负责。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">10. 终止</h2>\n              <p className=\"text-gray-700 mb-4\">\n                我们可能在以下情况下终止您的账户：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>违反本使用条款</li>\n                <li>从事非法或有害活动</li>\n                <li>长期不活跃</li>\n                <li>其他我们认为必要的情况</li>\n              </ul>\n              <p className=\"text-gray-700 mt-4\">\n                您也可以随时删除您的账户。账户终止后，您将失去访问服务的权利。\n              </p>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">11. 争议解决</h2>\n              <p className=\"text-gray-700 mb-4\">\n                如果发生争议：\n              </p>\n              <ul className=\"list-disc pl-6 text-gray-700 space-y-2\">\n                <li>首先尝试通过友好协商解决</li>\n                <li>协商不成的，提交我们所在地有管辖权的人民法院</li>\n                <li>适用中华人民共和国法律</li>\n              </ul>\n            </section>\n\n            <section className=\"mb-8\">\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">12. 联系信息</h2>\n              <p className=\"text-gray-700 mb-4\">\n                如果您对本使用条款有任何疑问，请联系我们：\n              </p>\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <p className=\"text-gray-700\"><strong>邮箱：</strong> <EMAIL></p>\n                <p className=\"text-gray-700\"><strong>电话：</strong> 400-123-4567</p>\n                <p className=\"text-gray-700\"><strong>地址：</strong> 北京市朝阳区创新大厦</p>\n                <p className=\"text-gray-700\"><strong>工作时间：</strong> 周一至周五 9:00-18:00</p>\n              </div>\n            </section>\n\n            <div className=\"mt-12 p-6 bg-amber-50 rounded-lg border border-amber-200\">\n              <h3 className=\"text-lg font-semibold text-amber-900 mb-2\">重要提醒</h3>\n              <p className=\"text-amber-800\">\n                请仔细阅读并理解本使用条款。如果您是未成年人，请在家长或监护人的指导下使用我们的服务。\n                继续使用服务即表示您同意遵守这些条款。\n              </p>\n            </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IACA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;;gCAAqB;gCAC1B,SAAS,kBAAkB,GAAG,IAAI,KAAK,SAAS,kBAAkB,EAAE,kBAAkB,CAAC,WAAW;;;;;;;;;;;;;8BAI5G,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,aAAa,iBACrB,6LAAC;4BACC,WAAU;4BACV,yBAAyB;gCAAE,QAAQ,SAAS,aAAa,CAAC,OAAO,CAAC,OAAO;4BAAQ;;;;;iDAGnF,6LAAC;;8CACC,6LAAC;oCAAQ,WAAU;;sDACrB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAIlC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC;0DAAO;;;;;;;;;;;sDAEV,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;sDAGN,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC;0DAAO;;;;;;;;;;;sDAEV,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAIlC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;sDAEN,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;sDAEN,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;sDAEN,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;sEAAgB,6LAAC;sEAAO;;;;;;wDAAY;;;;;;;8DACjD,6LAAC;oDAAE,WAAU;;sEAAgB,6LAAC;sEAAO;;;;;;wDAAY;;;;;;;8DACjD,6LAAC;oDAAE,WAAU;;sEAAgB,6LAAC;sEAAO;;;;;;wDAAY;;;;;;;8DACjD,6LAAC;oDAAE,WAAU;;sEAAgB,6LAAC;sEAAO;;;;;;wDAAc;;;;;;;;;;;;;;;;;;;8CAIvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5C;GAvPwB;KAAA", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string) {\n  return new Date(date).toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport function formatDateTime(date: Date | string) {\n  return new Date(date).toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  })\n}\n\nexport function truncateText(text: string, maxLength: number) {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,MAAM,cAAc,CAAC,SAAS;QAC5C,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/hooks/useSettings.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface PublicSettings {\n  // 基本信息\n  site_name: string\n  site_description: string\n  site_slogan: string\n  team_count: number\n  copyright_year: string\n\n  // 联系信息\n  contact_phone: string\n  contact_email: string\n  contact_address: string\n  contact_wechat: string\n  contact_qq: string\n\n  // 功能开关\n  allow_registration: boolean\n  show_team_section: boolean\n  show_projects_section: boolean\n  show_blog_section: boolean\n  show_apply_section: boolean\n  enable_comments: boolean\n  enable_likes: boolean\n\n  // 外观设置\n  primary_color: string\n  secondary_color: string\n  hero_background: string\n  show_animations: boolean\n  dark_mode_enabled: boolean\n\n  // 首页内容\n  hero_title: string\n  hero_subtitle: string\n  hero_description: string\n  about_intro: string\n\n  // 统计数据\n  stats_projects: number\n  stats_students: number\n  stats_satisfaction: number\n\n  // SEO设置\n  meta_keywords: string\n  meta_description: string\n}\n\nconst defaultSettings: PublicSettings = {\n  // 基本信息\n  site_name: '创客教育平台',\n  site_description: '专业的STEAM教育平台',\n  site_slogan: '创新思维 · 实践能力',\n  team_count: 50,\n  copyright_year: '2024',\n\n  // 联系信息\n  contact_phone: '************',\n  contact_email: '<EMAIL>',\n  contact_address: '北京市海淀区创新大厦',\n  contact_wechat: 'makeredu2024',\n  contact_qq: '123456789',\n\n  // 功能开关\n  allow_registration: true,\n  show_team_section: true,\n  show_projects_section: true,\n  show_blog_section: true,\n  show_apply_section: true,\n  enable_comments: true,\n  enable_likes: true,\n\n  // 外观设置\n  primary_color: '#3B82F6',\n  secondary_color: '#6366F1',\n  hero_background: 'gradient',\n  show_animations: true,\n  dark_mode_enabled: false,\n\n  // 首页内容\n  hero_title: '创新思维 · 实践能力',\n  hero_subtitle: 'STEAM创客教育平台',\n  hero_description: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',\n  about_intro: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',\n\n  // 统计数据\n  stats_projects: 500,\n  stats_students: 1000,\n  stats_satisfaction: 98,\n\n  // SEO设置\n  meta_keywords: 'STEAM教育,创客教育,3D打印,机器人,编程',\n  meta_description: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程'\n}\n\nexport function useSettings() {\n  const [settings, setSettings] = useState<PublicSettings>(defaultSettings)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    fetchSettings()\n  }, [])\n\n  const fetchSettings = async () => {\n    try {\n      const response = await fetch('/api/settings/public')\n      if (response.ok) {\n        const data = await response.json()\n        setSettings({ ...defaultSettings, ...data })\n      }\n    } catch (error) {\n      console.error('Failed to fetch settings:', error)\n      // 使用默认设置\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return { settings, isLoading, refetch: fetchSettings }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAmDA,MAAM,kBAAkC;IACtC,OAAO;IACP,WAAW;IACX,kBAAkB;IAClB,aAAa;IACb,YAAY;IACZ,gBAAgB;IAEhB,OAAO;IACP,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,gBAAgB;IAChB,YAAY;IAEZ,OAAO;IACP,oBAAoB;IACpB,mBAAmB;IACnB,uBAAuB;IACvB,mBAAmB;IACnB,oBAAoB;IACpB,iBAAiB;IACjB,cAAc;IAEd,OAAO;IACP,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,mBAAmB;IAEnB,OAAO;IACP,YAAY;IACZ,eAAe;IACf,kBAAkB;IAClB,aAAa;IAEb,OAAO;IACP,gBAAgB;IAChB,gBAAgB;IAChB,oBAAoB;IAEpB,QAAQ;IACR,eAAe;IACf,kBAAkB;AACpB;AAEO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY;oBAAE,GAAG,eAAe;oBAAE,GAAG,IAAI;gBAAC;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,OAAO;QAAE;QAAU;QAAW,SAAS;IAAc;AACvD;GAxBgB", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/layout/navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { Menu, X, User, LogOut, Settings } from 'lucide-react'\nimport { useSettings } from '@/hooks/useSettings'\n\nexport default function Navbar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const { data: session, status } = useSession()\n  const { settings } = useSettings()\n\n  // 根据设置动态生成导航菜单\n  const navigation = [\n    { name: '首页', href: '/' },\n    { name: '关于我们', href: '/about' },\n    ...(settings.show_projects_section ? [{ name: '作品展示', href: '/projects' }] : []),\n    ...(settings.show_team_section ? [{ name: '团队介绍', href: '/team' }] : []),\n    ...(settings.show_apply_section ? [{ name: '申请加入', href: '/apply' }] : []),\n    ...(settings.show_blog_section ? [{ name: '博客', href: '/blog' }] : []),\n  ]\n\n  return (\n    <nav className=\"bg-white/95 backdrop-blur-md shadow-enhanced sticky top-0 z-50 border-b border-gray-100\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex-shrink-0 flex items-center group\">\n              <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\n                <span className=\"text-2xl font-bold group-hover:scale-105 transition-transform duration-200\">\n                  创客教育\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-2\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"nav-item text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* User Menu */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse bg-gray-200 h-8 w-20 rounded\"></div>\n            ) : session ? (\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-700\">\n                  欢迎, {session.user.name}\n                </span>\n                {session.user.role === 'ADMIN' && (\n                  <Link href=\"/admin\">\n                    <Button variant=\"outline\" size=\"sm\" className=\"btn-enhanced rounded-xl\">\n                      <Settings className=\"w-4 h-4 mr-2\" />\n                      管理后台\n                    </Button>\n                  </Link>\n                )}\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => signOut()}\n                  className=\"btn-enhanced rounded-xl\"\n                >\n                  <LogOut className=\"w-4 h-4 mr-2\" />\n                  退出\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <Link href=\"/auth/signin\">\n                  <Button variant=\"outline\" size=\"sm\" className=\"btn-enhanced rounded-xl\">\n                    登录\n                  </Button>\n                </Link>\n                <Link href=\"/auth/signup\">\n                  <Button size=\"sm\" className=\"btn-enhanced rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700\">\n                    注册\n                  </Button>\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600 p-2 rounded-xl hover:bg-blue-50 transition-all duration-200\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <div className=\"md:hidden animate-slide-up\">\n          <div className=\"px-4 pt-4 pb-6 space-y-2 bg-white/95 backdrop-blur-md border-t border-gray-100\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 hover:bg-blue-50 block px-4 py-3 rounded-xl text-base font-medium transition-all duration-200\"\n                onClick={() => setIsOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n            \n            {/* Mobile User Menu */}\n            <div className=\"border-t pt-4 mt-4\">\n              {session ? (\n                <div className=\"space-y-2\">\n                  <div className=\"px-3 py-2 text-sm text-gray-700\">\n                    欢迎, {session.user.name}\n                  </div>\n                  {session.user.role === 'ADMIN' && (\n                    <Link\n                      href=\"/admin\"\n                      className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      管理后台\n                    </Link>\n                  )}\n                  <button\n                    onClick={() => {\n                      signOut()\n                      setIsOpen(false)\n                    }}\n                    className=\"block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600\"\n                  >\n                    退出\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  <Link\n                    href=\"/auth/signin\"\n                    className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n                    onClick={() => setIsOpen(false)}\n                  >\n                    登录\n                  </Link>\n                  <Link\n                    href=\"/auth/signup\"\n                    className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n                    onClick={() => setIsOpen(false)}\n                  >\n                    注册\n                  </Link>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE/B,eAAe;IACf,MAAM,aAAa;QACjB;YAAE,MAAM;YAAM,MAAM;QAAI;QACxB;YAAE,MAAM;YAAQ,MAAM;QAAS;WAC3B,SAAS,qBAAqB,GAAG;YAAC;gBAAE,MAAM;gBAAQ,MAAM;YAAY;SAAE,GAAG,EAAE;WAC3E,SAAS,iBAAiB,GAAG;YAAC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAAE,GAAG,EAAE;WACnE,SAAS,kBAAkB,GAAG;YAAC;gBAAE,MAAM;gBAAQ,MAAM;YAAS;SAAE,GAAG,EAAE;WACrE,SAAS,iBAAiB,GAAG;YAAC;gBAAE,MAAM;gBAAM,MAAM;YAAQ;SAAE,GAAG,EAAE;KACtE;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;;;;;;;;;;sCAQnG,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,6LAAC;4BAAI,WAAU;sCACZ,WAAW,0BACV,6LAAC;gCAAI,WAAU;;;;;uCACb,wBACF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAwB;4CACjC,QAAQ,IAAI,CAAC,IAAI;;;;;;;oCAEvB,QAAQ,IAAI,CAAC,IAAI,KAAK,yBACrB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;;8DAC5C,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAK3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD;wCACrB,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;qDAKvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAA0B;;;;;;;;;;;kDAI1E,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA+G;;;;;;;;;;;;;;;;;;;;;;sCASnJ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO7D,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,UAAU;0CAExB,KAAK,IAAI;+BALL,KAAK,IAAI;;;;;sCAUlB,6LAAC;4BAAI,WAAU;sCACZ,wBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAkC;4CAC1C,QAAQ,IAAI,CAAC,IAAI;;;;;;;oCAEvB,QAAQ,IAAI,CAAC,IAAI,KAAK,yBACrB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,UAAU;kDAC1B;;;;;;kDAIH,6LAAC;wCACC,SAAS;4CACP,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD;4CACN,UAAU;wCACZ;wCACA,WAAU;kDACX;;;;;;;;;;;qDAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,UAAU;kDAC1B;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,UAAU;kDAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAlKwB;;QAEY,iJAAA,CAAA,aAAU;QACvB,8HAAA,CAAA,cAAW;;;KAHV", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Mail, Phone, MapPin } from 'lucide-react'\nimport { useSettings } from '@/hooks/useSettings'\n\nexport default function Footer() {\n  const { settings } = useSettings()\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* 品牌信息 */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <h3 className=\"text-2xl font-bold text-blue-400 mb-4\">{settings.site_name}</h3>\n            <p className=\"text-gray-300 mb-4\">\n              {settings.site_description}\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <span>{settings.contact_email}</span>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                <span>{settings.contact_phone}</span>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                <span>{settings.contact_address}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* 快速链接 */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">快速链接</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  关于我们\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/projects\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  作品展示\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/team\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  团队介绍\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/apply\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  申请加入\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/blog\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  博客文章\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* 教育资源 */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">教育资源</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/courses\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  STEAM课程\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/projects?category=THREE_D_PRINTING\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  3D打印项目\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/projects?category=ROBOTICS\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  机器人制作\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/projects?category=PROGRAMMING\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  编程教学\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/resources\" className=\"text-gray-300 hover:text-blue-400 transition-colors\">\n                  学习资源\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-700 mt-8 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"text-gray-300 text-sm\">\n              © {settings.copyright_year} {settings.site_name}. 保留所有权利.\n            </div>\n            <div className=\"flex space-x-6 mt-4 md:mt-0\">\n              <Link href=\"/privacy\" className=\"text-gray-300 hover:text-blue-400 text-sm transition-colors\">\n                隐私政策\n              </Link>\n              <Link href=\"/terms\" className=\"text-gray-300 hover:text-blue-400 text-sm transition-colors\">\n                使用条款\n              </Link>\n              <Link href=\"/contact\" className=\"text-gray-300 hover:text-blue-400 text-sm transition-colors\">\n                联系我们\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;;;;;;AAEe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC,SAAS,SAAS;;;;;;8CACzE,6LAAC;oCAAE,WAAU;8CACV,SAAS,gBAAgB;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,SAAS,aAAa;;;;;;;;;;;;sDAE/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAM,SAAS,aAAa;;;;;;;;;;;;sDAE/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAM,SAAS,eAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAMrC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsD;;;;;;;;;;;sDAItF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAsD;;;;;;;;;;;sDAIzF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAsD;;;;;;;;;;;sDAIrF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsD;;;;;;;;;;;sDAItF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAsD;;;;;;;;;;;sDAIxF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsC,WAAU;0DAAsD;;;;;;;;;;;sDAInH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA8B,WAAU;0DAAsD;;;;;;;;;;;sDAI3G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiC,WAAU;0DAAsD;;;;;;;;;;;sDAI9G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhG,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAwB;oCAClC,SAAS,cAAc;oCAAC;oCAAE,SAAS,SAAS;oCAAC;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8D;;;;;;kDAG9F,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA8D;;;;;;kDAG5F,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5G;GAnHwB;;QACD,8HAAA,CAAA,cAAW;;;KADV", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/DynamicStyles.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useSettings } from '@/hooks/useSettings'\n\nexport default function DynamicStyles() {\n  const { settings } = useSettings()\n\n  useEffect(() => {\n    // 创建动态样式\n    const styleId = 'dynamic-styles'\n    let styleElement = document.getElementById(styleId) as HTMLStyleElement\n    \n    if (!styleElement) {\n      styleElement = document.createElement('style')\n      styleElement.id = styleId\n      document.head.appendChild(styleElement)\n    }\n\n    // 生成CSS\n    const css = `\n      :root {\n        --primary-color: ${settings.primary_color};\n        --secondary-color: ${settings.secondary_color};\n      }\n\n      /* 主题色应用 */\n      .bg-primary {\n        background-color: var(--primary-color) !important;\n      }\n\n      .text-primary {\n        color: var(--primary-color) !important;\n      }\n\n      .border-primary {\n        border-color: var(--primary-color) !important;\n      }\n\n      .bg-secondary {\n        background-color: var(--secondary-color) !important;\n      }\n\n      .text-secondary {\n        color: var(--secondary-color) !important;\n      }\n\n      /* 动态按钮样式 */\n      .btn-primary {\n        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;\n        border: none !important;\n        color: white !important;\n      }\n\n      .btn-primary:hover {\n        background: linear-gradient(135deg, \n          color-mix(in srgb, var(--primary-color) 90%, black), \n          color-mix(in srgb, var(--secondary-color) 90%, black)\n        ) !important;\n      }\n\n      /* 动态链接样式 */\n      .link-primary {\n        color: var(--primary-color) !important;\n      }\n\n      .link-primary:hover {\n        color: var(--secondary-color) !important;\n      }\n\n      /* 动态渐变背景 */\n      .bg-gradient-primary {\n        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;\n      }\n\n      .bg-gradient-primary-light {\n        background: linear-gradient(135deg, \n          color-mix(in srgb, var(--primary-color) 10%, white), \n          color-mix(in srgb, var(--secondary-color) 10%, white)\n        ) !important;\n      }\n\n      /* 动画控制 */\n      ${!settings.show_animations ? `\n        *, *::before, *::after {\n          animation-duration: 0s !important;\n          animation-delay: 0s !important;\n          transition-duration: 0s !important;\n          transition-delay: 0s !important;\n        }\n      ` : ''}\n\n      /* 暗色模式 */\n      ${settings.dark_mode_enabled ? `\n        .dark {\n          --bg-primary: #1a1a1a;\n          --bg-secondary: #2d2d2d;\n          --text-primary: #ffffff;\n          --text-secondary: #cccccc;\n        }\n\n        .dark body {\n          background-color: var(--bg-primary);\n          color: var(--text-primary);\n        }\n\n        .dark .bg-white {\n          background-color: var(--bg-secondary) !important;\n        }\n\n        .dark .text-gray-900 {\n          color: var(--text-primary) !important;\n        }\n\n        .dark .text-gray-600 {\n          color: var(--text-secondary) !important;\n        }\n\n        .dark .border-gray-200 {\n          border-color: #404040 !important;\n        }\n      ` : ''}\n\n      /* 自定义滚动条 */\n      ::-webkit-scrollbar {\n        width: 8px;\n      }\n\n      ::-webkit-scrollbar-track {\n        background: #f1f1f1;\n        border-radius: 4px;\n      }\n\n      ::-webkit-scrollbar-thumb {\n        background: var(--primary-color);\n        border-radius: 4px;\n      }\n\n      ::-webkit-scrollbar-thumb:hover {\n        background: var(--secondary-color);\n      }\n\n      /* 选择文本颜色 */\n      ::selection {\n        background-color: var(--primary-color);\n        color: white;\n      }\n\n      ::-moz-selection {\n        background-color: var(--primary-color);\n        color: white;\n      }\n\n      /* 焦点样式 */\n      .focus\\\\:ring-primary:focus {\n        --tw-ring-color: var(--primary-color) !important;\n      }\n\n      /* 加载动画颜色 */\n      .loading-spinner {\n        border-top-color: var(--primary-color) !important;\n      }\n\n      /* 进度条样式 */\n      .progress-bar {\n        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color)) !important;\n      }\n    `\n\n    styleElement.textContent = css\n  }, [settings])\n\n  return null\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,SAAS;YACT,MAAM,UAAU;YAChB,IAAI,eAAe,SAAS,cAAc,CAAC;YAE3C,IAAI,CAAC,cAAc;gBACjB,eAAe,SAAS,aAAa,CAAC;gBACtC,aAAa,EAAE,GAAG;gBAClB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;YAEA,QAAQ;YACR,MAAM,MAAM,CAAC;;yBAEQ,EAAE,SAAS,aAAa,CAAC;2BACvB,EAAE,SAAS,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA4DhD,EAAE,CAAC,SAAS,eAAe,GAAG,CAAC;;;;;;;MAO/B,CAAC,GAAG,GAAG;;;MAGP,EAAE,SAAS,iBAAiB,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA4BhC,CAAC,GAAG,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8CT,CAAC;YAED,aAAa,WAAW,GAAG;QAC7B;kCAAG;QAAC;KAAS;IAEb,OAAO;AACT;GAxKwB;;QACD,8HAAA,CAAA,cAAW;;;KADV", "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { SessionProvider } from 'next-auth/react'\nimport Navbar from './navbar'\nimport Footer from './footer'\nimport DynamicStyles from '@/components/DynamicStyles'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n  session?: any\n}\n\nexport default function MainLayout({ children, session }: MainLayoutProps) {\n  return (\n    <SessionProvider session={session}>\n      <DynamicStyles />\n      <div className=\"min-h-screen flex flex-col\">\n        <Navbar />\n        <main className=\"flex-grow\">\n          {children}\n        </main>\n        <Footer />\n      </div>\n    </SessionProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYe,SAAS,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAmB;IACvE,qBACE,6LAAC,iJAAA,CAAA,kBAAe;QAAC,SAAS;;0BACxB,6LAAC,sIAAA,CAAA,UAAa;;;;;0BACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,UAAM;;;;;kCACP,6LAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;AAIf;KAbwB", "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/demo/%E5%88%9B%E5%AE%A2%E6%95%99%E8%82%B2/maker-education-platform/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { useSettings } from '@/hooks/useSettings'\n\ninterface ThemeContextType {\n  isDark: boolean\n  toggleTheme: () => void\n  primaryColor: string\n  secondaryColor: string\n  setPrimaryColor: (color: string) => void\n  setSecondaryColor: (color: string) => void\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined)\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const { settings } = useSettings()\n  const [isDark, setIsDark] = useState(false)\n  const [primaryColor, setPrimaryColor] = useState('#3B82F6')\n  const [secondaryColor, setSecondaryColor] = useState('#6366F1')\n\n  useEffect(() => {\n    // 从设置中获取主题配置\n    setIsDark(settings.dark_mode_enabled)\n    setPrimaryColor(settings.primary_color)\n    setSecondaryColor(settings.secondary_color)\n  }, [settings])\n\n  useEffect(() => {\n    // 应用主题到文档\n    const root = document.documentElement\n    \n    if (isDark) {\n      root.classList.add('dark')\n    } else {\n      root.classList.remove('dark')\n    }\n\n    // 设置CSS变量\n    root.style.setProperty('--primary-color', primaryColor)\n    root.style.setProperty('--secondary-color', secondaryColor)\n    \n    // 设置Tailwind CSS变量\n    root.style.setProperty('--color-primary-500', primaryColor)\n    root.style.setProperty('--color-secondary-500', secondaryColor)\n  }, [isDark, primaryColor, secondaryColor])\n\n  const toggleTheme = () => {\n    setIsDark(!isDark)\n  }\n\n  return (\n    <ThemeContext.Provider\n      value={{\n        isDark,\n        toggleTheme,\n        primaryColor,\n        secondaryColor,\n        setPrimaryColor,\n        setSecondaryColor,\n      }}\n    >\n      {children}\n    </ThemeContext.Provider>\n  )\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext)\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAcA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAAiC;;IACvE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,aAAa;YACb,UAAU,SAAS,iBAAiB;YACpC,gBAAgB,SAAS,aAAa;YACtC,kBAAkB,SAAS,eAAe;QAC5C;kCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,UAAU;YACV,MAAM,OAAO,SAAS,eAAe;YAErC,IAAI,QAAQ;gBACV,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;YAEA,UAAU;YACV,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB;YAC1C,KAAK,KAAK,CAAC,WAAW,CAAC,qBAAqB;YAE5C,mBAAmB;YACnB,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB;YAC9C,KAAK,KAAK,CAAC,WAAW,CAAC,yBAAyB;QAClD;kCAAG;QAAC;QAAQ;QAAc;KAAe;IAEzC,MAAM,cAAc;QAClB,UAAU,CAAC;IACb;IAEA,qBACE,6LAAC,aAAa,QAAQ;QACpB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAlDgB;;QACO,8HAAA,CAAA,cAAW;;;KADlB;AAoDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}
{"version": 3, "file": "clone-pseudos.js", "sourceRoot": "", "sources": ["../src/clone-pseudos.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,QAAQ,CAAA;AAIjD,SAAS,aAAa,CAAC,KAA0B;IAC/C,MAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;IACjD,OAAO,GAAG,KAAK,CAAC,OAAO,cAAc,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAA;AACtE,CAAC;AAED,SAAS,mBAAmB,CAAC,KAA0B,EAAE,OAAgB;IACvE,OAAO,kBAAkB,CAAC,OAAO,CAAC;SAC/B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACZ,MAAM,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;QAC1C,MAAM,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;QAEhD,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,CAAA;IAC7D,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,CAAC,CAAA;AACd,CAAC;AAED,SAAS,qBAAqB,CAC5B,SAAiB,EACjB,MAAc,EACd,KAA0B,EAC1B,OAAgB;IAEhB,MAAM,QAAQ,GAAG,IAAI,SAAS,IAAI,MAAM,EAAE,CAAA;IAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;QAC3B,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC;QACtB,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;IAEvC,OAAO,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,IAAI,OAAO,GAAG,CAAC,CAAA;AAC3D,CAAC;AAED,SAAS,kBAAkB,CACzB,UAAa,EACb,UAAa,EACb,MAAc,EACd,OAAgB;IAEhB,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;IACzD,MAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;IACjD,IAAI,OAAO,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;QACxC,OAAM;KACP;IAED,MAAM,SAAS,GAAG,IAAI,EAAE,CAAA;IACxB,IAAI;QACF,UAAU,CAAC,SAAS,GAAG,GAAG,UAAU,CAAC,SAAS,IAAI,SAAS,EAAE,CAAA;KAC9D;IAAC,OAAO,GAAG,EAAE;QACZ,OAAM;KACP;IAED,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IACpD,YAAY,CAAC,WAAW,CACtB,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CACzD,CAAA;IACD,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;AACtC,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,UAAa,EACb,UAAa,EACb,OAAgB;IAEhB,kBAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;IAC9D,kBAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;AAC/D,CAAC"}
{"version": 3, "file": "embed-webfonts.js", "sourceRoot": "", "sources": ["../src/embed-webfonts.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAA;AAChC,OAAO,EAAE,cAAc,EAAE,MAAM,WAAW,CAAA;AAC1C,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAA;AAO/D,MAAM,aAAa,GAAiC,EAAE,CAAA;AAEtD,KAAK,UAAU,QAAQ,CAAC,GAAW;IACjC,IAAI,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,CAAA;IAC9B,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,KAAK,CAAA;KACb;IAED,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA;IAC5B,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA;IAChC,KAAK,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAA;IAExB,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;IAE1B,OAAO,KAAK,CAAA;AACd,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,IAAc,EAAE,OAAgB;IACxD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;IAC1B,MAAM,QAAQ,GAAG,6BAA6B,CAAA;IAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,CAAA;IACrD,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,GAAW,EAAE,EAAE;QACnD,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACrC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAC/B,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAA;SAClC;QAED,OAAO,cAAc,CACnB,GAAG,EACH,OAAO,CAAC,gBAAgB,EACxB,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;YACb,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,MAAM,GAAG,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QACtB,CAAC,CACF,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAA;AACnD,CAAC;AAED,SAAS,QAAQ,CAAC,MAAc;IAC9B,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,OAAO,EAAE,CAAA;KACV;IAED,MAAM,MAAM,GAAa,EAAE,CAAA;IAC3B,MAAM,aAAa,GAAG,sBAAsB,CAAA;IAC5C,qBAAqB;IACrB,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;IAE/C,iDAAiD;IACjD,MAAM,cAAc,GAAG,IAAI,MAAM,CAC/B,kDAAkD,EAClD,IAAI,CACL,CAAA;IAED,iDAAiD;IACjD,OAAO,IAAI,EAAE;QACX,MAAM,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC5C,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,MAAK;SACN;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;KACxB;IACD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAA;IAE7C,MAAM,WAAW,GAAG,wCAAwC,CAAA;IAC5D,wCAAwC;IACxC,MAAM,gBAAgB,GACpB,uDAAuD;QACvD,uDAAuD,CAAA;IACzD,gBAAgB;IAChB,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;IAEvD,iDAAiD;IACjD,OAAO,IAAI,EAAE;QACX,IAAI,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACvC,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACpC,IAAI,OAAO,KAAK,IAAI,EAAE;gBACpB,MAAK;aACN;iBAAM;gBACL,WAAW,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAA;aAC/C;SACF;aAAM;YACL,YAAY,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAA;SAC/C;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;KACxB;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,KAAK,UAAU,WAAW,CACxB,WAA4B,EAC5B,OAAgB;IAEhB,MAAM,GAAG,GAAmB,EAAE,CAAA;IAC9B,MAAM,SAAS,GAA6B,EAAE,CAAA;IAE9C,6BAA6B;IAC7B,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5B,IAAI,UAAU,IAAI,KAAK,EAAE;YACvB,IAAI;gBACF,OAAO,CAAU,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;oBAC7D,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,WAAW,EAAE;wBACrC,IAAI,WAAW,GAAG,KAAK,GAAG,CAAC,CAAA;wBAC3B,MAAM,GAAG,GAAI,IAAsB,CAAC,IAAI,CAAA;wBACxC,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC;6BAC3B,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;6BACjD,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAChB,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;4BACjC,IAAI;gCACF,KAAK,CAAC,UAAU,CACd,IAAI,EACJ,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;oCACxB,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC;oCACpB,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAC1B,CAAA;6BACF;4BAAC,OAAO,KAAK,EAAE;gCACd,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE;oCACpD,IAAI;oCACJ,KAAK;iCACN,CAAC,CAAA;6BACH;wBACH,CAAC,CAAC,CACH;6BACA,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;4BACX,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;wBACzD,CAAC,CAAC,CAAA;wBAEJ,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;qBACzB;gBACH,CAAC,CAAC,CAAA;aACH;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,MAAM,GACV,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;gBACpE,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;oBACtB,SAAS,CAAC,IAAI,CACZ,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;yBACjB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;yBACjD,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAChB,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;wBACjC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;oBACjD,CAAC,CAAC,CACH;yBACA,KAAK,CAAC,CAAC,GAAY,EAAE,EAAE;wBACtB,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAA;oBACvD,CAAC,CAAC,CACL,CAAA;iBACF;gBACD,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,CAAC,CAAC,CAAA;aACnD;SACF;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;QACtC,2BAA2B;QAC3B,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5B,IAAI,UAAU,IAAI,KAAK,EAAE;gBACvB,IAAI;oBACF,OAAO,CAAe,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;wBAC3D,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAChB,CAAC,CAAC,CAAA;iBACH;gBAAC,OAAO,CAAC,EAAE;oBACV,OAAO,CAAC,KAAK,CAAC,sCAAsC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;iBACrE;aACF;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,GAAG,CAAA;IACZ,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,QAAwB;IAC/C,OAAO,QAAQ;SACZ,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,cAAc,CAAC;SACtD,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AACtE,CAAC;AAED,KAAK,UAAU,iBAAiB,CAC9B,IAAO,EACP,OAAgB;IAEhB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;KAC7D;IAED,MAAM,WAAW,GAAG,OAAO,CAAgB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA;IAC1E,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;IAExD,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAA;AAClC,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAY;IACvC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;AACzC,CAAC;AAED,SAAS,YAAY,CAAC,IAAiB;IACrC,MAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAA;IAC/B,SAAS,QAAQ,CAAC,IAAiB;QACjC,MAAM,UAAU,GACd,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,UAAU,CAAA;QAC5D,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrC,KAAK,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QAEF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1C,IAAI,KAAK,YAAY,WAAW,EAAE;gBAChC,QAAQ,CAAC,KAAK,CAAC,CAAA;aAChB;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IACD,QAAQ,CAAC,IAAI,CAAC,CAAA;IACd,OAAO,KAAK,CAAA;AACd,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,IAAO,EACP,OAAgB;IAEhB,MAAM,KAAK,GAAG,MAAM,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACpD,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;IACpC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAChC,KAAK;SACF,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CACf,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAC1D;SACA,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACZ,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB;YACnC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC5B,CAAC,CAAC,IAAI,CAAA;QACR,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;IACvD,CAAC,CAAC,CACL,CAAA;IAED,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAC5B,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,UAAa,EACb,OAAgB;IAEhB,MAAM,OAAO,GACX,OAAO,CAAC,YAAY,IAAI,IAAI;QAC1B,CAAC,CAAC,OAAO,CAAC,YAAY;QACtB,CAAC,CAAC,OAAO,CAAC,SAAS;YACnB,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,MAAM,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IAE9C,IAAI,OAAO,EAAE;QACX,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;QACjD,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;QAErD,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;QAEnC,IAAI,UAAU,CAAC,UAAU,EAAE;YACzB,UAAU,CAAC,YAAY,CAAC,SAAS,EAAE,UAAU,CAAC,UAAU,CAAC,CAAA;SAC1D;aAAM;YACL,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;SAClC;KACF;AACH,CAAC"}
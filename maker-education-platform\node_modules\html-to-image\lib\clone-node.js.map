{"version": 3, "file": "clone-node.js", "sourceRoot": "", "sources": ["../src/clone-node.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,iDAAqD;AACrD,+BAKe;AACf,iCAAqC;AACrC,qCAA6C;AAE7C,SAAe,kBAAkB,CAAC,MAAyB;;;;YACnD,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAA;YAClC,IAAI,OAAO,KAAK,QAAQ,EAAE;gBACxB,sBAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAsB,EAAA;aACpD;YACD,sBAAO,IAAA,kBAAW,EAAC,OAAO,CAAC,EAAA;;;CAC5B;AAED,SAAe,iBAAiB,CAAC,KAAuB,EAAE,OAAgB;;;;;;oBACxE,IAAI,KAAK,CAAC,UAAU,EAAE;wBACd,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;wBACzC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;wBACnC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAA;wBAChC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAA;wBAClC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;wBAClD,YAAU,MAAM,CAAC,SAAS,EAAE,CAAA;wBAClC,sBAAO,IAAA,kBAAW,EAAC,SAAO,CAAC,EAAA;qBAC5B;oBAEK,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;oBACrB,WAAW,GAAG,IAAA,mBAAW,EAAC,MAAM,CAAC,CAAA;oBACvB,qBAAM,IAAA,2BAAiB,EAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,EAAA;;oBAA/D,OAAO,GAAG,SAAqD;oBACrE,sBAAO,IAAA,kBAAW,EAAC,OAAO,CAAC,EAAA;;;;CAC5B;AAED,SAAe,kBAAkB,CAAC,MAAyB,EAAE,OAAgB;;;;;;;;yBAErE,CAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,eAAe,0CAAE,IAAI,CAAA,EAA7B,wBAA6B;oBACvB,qBAAM,SAAS,CACrB,MAAM,CAAC,eAAe,CAAC,IAAI,EAC3B,OAAO,EACP,IAAI,CACL,EAAA;wBAJD,sBAAO,CAAC,SAIP,CAAoB,EAAA;;;;;wBAMzB,sBAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAsB,EAAA;;;;CACpD;AAED,SAAe,eAAe,CAC5B,IAAO,EACP,OAAgB;;;YAEhB,IAAI,IAAA,0BAAmB,EAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE;gBAChD,sBAAO,kBAAkB,CAAC,IAAI,CAAC,EAAA;aAChC;YAED,IAAI,IAAA,0BAAmB,EAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;gBAC/C,sBAAO,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAA;aACxC;YAED,IAAI,IAAA,0BAAmB,EAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE;gBAChD,sBAAO,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAA;aACzC;YAED,sBAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAM,EAAA;;;CAC/C;AAED,IAAM,aAAa,GAAG,UAAC,IAAiB;IACtC,OAAA,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,MAAM;AAA7D,CAA6D,CAAA;AAE/D,IAAM,YAAY,GAAG,UAAC,IAAiB;IACrC,OAAA,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK;AAA5D,CAA4D,CAAA;AAE9D,SAAe,aAAa,CAC1B,UAAa,EACb,UAAa,EACb,OAAgB;;;;;;;oBAEhB,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE;wBAC5B,sBAAO,UAAU,EAAA;qBAClB;oBAEG,QAAQ,GAAQ,EAAE,CAAA;oBAEtB,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,aAAa,EAAE;wBACzD,QAAQ,GAAG,IAAA,cAAO,EAAI,UAAU,CAAC,aAAa,EAAE,CAAC,CAAA;qBAClD;yBAAM,IACL,IAAA,0BAAmB,EAAC,UAAU,EAAE,iBAAiB,CAAC;yBAClD,MAAA,UAAU,CAAC,eAAe,0CAAE,IAAI,CAAA,EAChC;wBACA,QAAQ,GAAG,IAAA,cAAO,EAAI,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;qBAClE;yBAAM;wBACL,QAAQ,GAAG,IAAA,cAAO,EAAI,CAAC,MAAA,UAAU,CAAC,UAAU,mCAAI,UAAU,CAAC,CAAC,UAAU,CAAC,CAAA;qBACxE;oBAED,IACE,QAAQ,CAAC,MAAM,KAAK,CAAC;wBACrB,IAAA,0BAAmB,EAAC,UAAU,EAAE,gBAAgB,CAAC,EACjD;wBACA,sBAAO,UAAU,EAAA;qBAClB;oBAED,qBAAM,QAAQ,CAAC,MAAM,CACnB,UAAC,QAAQ,EAAE,KAAK;4BACd,OAAA,QAAQ;iCACL,IAAI,CAAC,cAAM,OAAA,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,EAAzB,CAAyB,CAAC;iCACrC,IAAI,CAAC,UAAC,WAA+B;gCACpC,IAAI,WAAW,EAAE;oCACf,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;iCACpC;4BACH,CAAC,CAAC;wBANJ,CAMI,EACN,OAAO,CAAC,OAAO,EAAE,CAClB,EAAA;;oBAVD,SAUC,CAAA;oBAED,sBAAO,UAAU,EAAA;;;;CAClB;AAED,SAAS,aAAa,CACpB,UAAa,EACb,UAAa,EACb,OAAgB;IAEhB,IAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAA;IACpC,IAAI,CAAC,WAAW,EAAE;QAChB,OAAM;KACP;IAED,IAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACvD,IAAI,WAAW,CAAC,OAAO,EAAE;QACvB,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAA;QACzC,WAAW,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAA;KAC1D;SAAM;QACL,IAAA,yBAAkB,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;YACvC,IAAI,KAAK,GAAG,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAChD,IAAM,WAAW,GACf,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;gBACpE,KAAK,GAAG,UAAG,WAAW,OAAI,CAAA;aAC3B;YAED,IACE,IAAA,0BAAmB,EAAC,UAAU,EAAE,iBAAiB,CAAC;gBAClD,IAAI,KAAK,SAAS;gBAClB,KAAK,KAAK,QAAQ,EAClB;gBACA,KAAK,GAAG,OAAO,CAAA;aAChB;YAED,IAAI,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;gBAChD,KAAK,GAAG,eAAQ,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,MAAG,CAAA;aAChD;YAED,WAAW,CAAC,WAAW,CACrB,IAAI,EACJ,KAAK,EACL,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,CACtC,CAAA;QACH,CAAC,CAAC,CAAA;KACH;AACH,CAAC;AAED,SAAS,eAAe,CAAwB,UAAa,EAAE,UAAa;IAC1E,IAAI,IAAA,0BAAmB,EAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE;QACxD,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAA;KACxC;IAED,IAAI,IAAA,0BAAmB,EAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE;QACrD,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,CAAA;KACnD;AACH,CAAC;AAED,SAAS,gBAAgB,CAAwB,UAAa,EAAE,UAAa;IAC3E,IAAI,IAAA,0BAAmB,EAAC,UAAU,EAAE,iBAAiB,CAAC,EAAE;QACtD,IAAM,YAAY,GAAG,UAAsC,CAAA;QAC3D,IAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAC3D,UAAC,KAAK,IAAK,OAAA,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,EAAhD,CAAgD,CAC5D,CAAA;QAED,IAAI,cAAc,EAAE;YAClB,cAAc,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;SAC5C;KACF;AACH,CAAC;AAED,SAAS,QAAQ,CACf,UAAa,EACb,UAAa,EACb,OAAgB;IAEhB,IAAI,IAAA,0BAAmB,EAAC,UAAU,EAAE,OAAO,CAAC,EAAE;QAC5C,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;QAC9C,IAAA,mCAAmB,EAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;QACpD,eAAe,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QACvC,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;KACzC;IAED,OAAO,UAAU,CAAA;AACnB,CAAC;AAED,SAAe,gBAAgB,CAC7B,KAAQ,EACR,OAAgB;;;;;;oBAEV,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;oBACxE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,sBAAO,KAAK,EAAA;qBACb;oBAEK,aAAa,GAAmC,EAAE,CAAA;oBAC/C,CAAC,GAAG,CAAC;;;yBAAE,CAAA,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;oBACvB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;oBACb,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAA;yBACrC,EAAE,EAAF,wBAAE;oBACE,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;oBAC/B,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAgB,CAAA;yBACxD,CAAA,CAAC,KAAK,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA,EAA1C,wBAA0C;oBAC5C,4CAA4C;oBAC5C,KAAA,aAAa,CAAA;oBAAC,KAAA,EAAE,CAAA;oBAAK,qBAAM,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,EAAA;;oBAD/D,4CAA4C;oBAC5C,MAAiB,GAAG,CAAC,SAA0C,CAAE,CAAA;;;oBARtC,CAAC,EAAE,CAAA;;;oBAa9B,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;oBAC1C,IAAI,KAAK,CAAC,MAAM,EAAE;wBACV,EAAE,GAAG,8BAA8B,CAAA;wBACnC,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;wBAC/C,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;wBAC7B,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAA;wBAC/B,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAA;wBACrB,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAA;wBACtB,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAA;wBAC7B,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAA;wBAEpB,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;wBACjD,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;wBAErB,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BACrC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;yBAC3B;wBAED,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;qBACvB;oBAED,sBAAO,KAAK,EAAA;;;;CACb;AAED,SAAsB,SAAS,CAC7B,IAAO,EACP,OAAgB,EAChB,MAAgB;;;YAEhB,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtD,sBAAO,IAAI,EAAA;aACZ;YAED,sBAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;qBACzB,IAAI,CAAC,UAAC,UAAU,IAAK,OAAA,eAAe,CAAC,UAAU,EAAE,OAAO,CAAe,EAAlD,CAAkD,CAAC;qBACxE,IAAI,CAAC,UAAC,UAAU,IAAK,OAAA,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,EAAxC,CAAwC,CAAC;qBAC9D,IAAI,CAAC,UAAC,UAAU,IAAK,OAAA,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,EAAnC,CAAmC,CAAC;qBACzD,IAAI,CAAC,UAAC,UAAU,IAAK,OAAA,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,EAArC,CAAqC,CAAC,EAAA;;;CAC/D;AAdD,8BAcC"}
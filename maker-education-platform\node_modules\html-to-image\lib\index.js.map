{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2CAAwC;AACxC,+CAA4C;AAC5C,6CAA0C;AAC1C,mDAA+D;AAC/D,+BAOe;AAEf,SAAsB,KAAK,CACzB,IAAO,EACP,OAAqB;IAArB,wBAAA,EAAA,YAAqB;;;;;;oBAEf,KAAoB,IAAA,mBAAY,EAAC,IAAI,EAAE,OAAO,CAAC,EAA7C,KAAK,WAAA,EAAE,MAAM,YAAA,CAAgC;oBACjC,qBAAM,IAAA,sBAAS,EAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,EAAA;;oBAAlD,UAAU,GAAG,CAAC,SAAoC,CAAgB;oBACxE,qBAAM,IAAA,8BAAa,EAAC,UAAU,EAAE,OAAO,CAAC,EAAA;;oBAAxC,SAAwC,CAAA;oBACxC,qBAAM,IAAA,0BAAW,EAAC,UAAU,EAAE,OAAO,CAAC,EAAA;;oBAAtC,SAAsC,CAAA;oBACtC,IAAA,wBAAU,EAAC,UAAU,EAAE,OAAO,CAAC,CAAA;oBACf,qBAAM,IAAA,oBAAa,EAAC,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,EAAA;;oBAAxD,OAAO,GAAG,SAA8C;oBAC9D,sBAAO,OAAO,EAAA;;;;CACf;AAXD,sBAWC;AAED,SAAsB,QAAQ,CAC5B,IAAO,EACP,OAAqB;IAArB,wBAAA,EAAA,YAAqB;;;;;;oBAEf,KAAoB,IAAA,mBAAY,EAAC,IAAI,EAAE,OAAO,CAAC,EAA7C,KAAK,WAAA,EAAE,MAAM,YAAA,CAAgC;oBACzC,qBAAM,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,EAAA;;oBAAhC,GAAG,GAAG,SAA0B;oBAC1B,qBAAM,IAAA,kBAAW,EAAC,GAAG,CAAC,EAAA;;oBAA5B,GAAG,GAAG,SAAsB;oBAE5B,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;oBACzC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAE,CAAA;oBAClC,KAAK,GAAG,OAAO,CAAC,UAAU,IAAI,IAAA,oBAAa,GAAE,CAAA;oBAC7C,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,KAAK,CAAA;oBAC1C,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,MAAM,CAAA;oBAEnD,MAAM,CAAC,KAAK,GAAG,WAAW,GAAG,KAAK,CAAA;oBAClC,MAAM,CAAC,MAAM,GAAG,YAAY,GAAG,KAAK,CAAA;oBAEpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;wBAC1B,IAAA,4BAAqB,EAAC,MAAM,CAAC,CAAA;qBAC9B;oBACD,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,UAAG,WAAW,CAAE,CAAA;oBACrC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,UAAG,YAAY,CAAE,CAAA;oBAEvC,IAAI,OAAO,CAAC,eAAe,EAAE;wBAC3B,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,eAAe,CAAA;wBAC3C,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;qBACpD;oBAED,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;oBAEzD,sBAAO,MAAM,EAAA;;;;CACd;AA/BD,4BA+BC;AAED,SAAsB,WAAW,CAC/B,IAAO,EACP,OAAqB;IAArB,wBAAA,EAAA,YAAqB;;;;;;oBAEf,KAAoB,IAAA,mBAAY,EAAC,IAAI,EAAE,OAAO,CAAC,EAA7C,KAAK,WAAA,EAAE,MAAM,YAAA,CAAgC;oBACtC,qBAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAA;;oBAAtC,MAAM,GAAG,SAA6B;oBACtC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAE,CAAA;oBACpC,sBAAO,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,EAAA;;;;CAClD;AARD,kCAQC;AAED,SAAsB,KAAK,CACzB,IAAO,EACP,OAAqB;IAArB,wBAAA,EAAA,YAAqB;;;;;wBAEN,qBAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAA;;oBAAtC,MAAM,GAAG,SAA6B;oBAC5C,sBAAO,MAAM,CAAC,SAAS,EAAE,EAAA;;;;CAC1B;AAND,sBAMC;AAED,SAAsB,MAAM,CAC1B,IAAO,EACP,OAAqB;IAArB,wBAAA,EAAA,YAAqB;;;;;wBAEN,qBAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAA;;oBAAtC,MAAM,GAAG,SAA6B;oBAC5C,sBAAO,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC,EAAA;;;;CAC5D;AAND,wBAMC;AAED,SAAsB,MAAM,CAC1B,IAAO,EACP,OAAqB;IAArB,wBAAA,EAAA,YAAqB;;;;;wBAEN,qBAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAA;;oBAAtC,MAAM,GAAG,SAA6B;oBAC/B,qBAAM,IAAA,mBAAY,EAAC,MAAM,CAAC,EAAA;;oBAAjC,IAAI,GAAG,SAA0B;oBACvC,sBAAO,IAAI,EAAA;;;;CACZ;AAPD,wBAOC;AAED,SAAsB,eAAe,CACnC,IAAO,EACP,OAAqB;IAArB,wBAAA,EAAA,YAAqB;;;YAErB,sBAAO,IAAA,8BAAa,EAAC,IAAI,EAAE,OAAO,CAAC,EAAA;;;CACpC;AALD,0CAKC"}
{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAgB,UAAU,CAAC,GAAW,EAAE,OAAsB;IAC5D,0BAA0B;IAC1B,IAAI,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;QAC9B,OAAO,GAAG,CAAA;KACX;IAED,4CAA4C;IAC5C,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;QACtB,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAA;KACtC;IAED,+BAA+B;IAC/B,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;QAC1B,OAAO,GAAG,CAAA;KACX;IAED,IAAM,GAAG,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAA;IACxD,IAAM,IAAI,GAAG,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;IACtC,IAAM,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;IAEhC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IAC1B,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;IAEvB,IAAI,OAAO,EAAE;QACX,IAAI,CAAC,IAAI,GAAG,OAAO,CAAA;KACpB;IAED,CAAC,CAAC,IAAI,GAAG,GAAG,CAAA;IAEZ,OAAO,CAAC,CAAC,IAAI,CAAA;AACf,CAAC;AA9BD,gCA8BC;AAEY,QAAA,IAAI,GAAG,CAAC;IACnB,kDAAkD;IAClD,mFAAmF;IACnF,IAAI,OAAO,GAAG,CAAC,CAAA;IAEf,kDAAkD;IAClD,IAAM,MAAM,GAAG;QACb,sCAAsC;QACtC,OAAA,cAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAA,EAAE,EAAI,CAAC,CAAA,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAAhE,CAAgE,CAAA;IAElE,OAAO;QACL,OAAO,IAAI,CAAC,CAAA;QACZ,OAAO,WAAI,MAAM,EAAE,SAAG,OAAO,CAAE,CAAA;IACjC,CAAC,CAAA;AACH,CAAC,CAAC,EAAE,CAAA;AAEJ,SAAgB,KAAK,CAAI,EAAU;IACjC,OAAO,UAAC,IAAO;QACb,OAAA,IAAI,OAAO,CAAI,UAAC,OAAO;YACrB,UAAU,CAAC,cAAM,OAAA,OAAO,CAAC,IAAI,CAAC,EAAb,CAAa,EAAE,EAAE,CAAC,CAAA;QACrC,CAAC,CAAC;IAFF,CAEE,CAAA;AACN,CAAC;AALD,sBAKC;AAED,SAAgB,OAAO,CAAI,SAAc;IACvC,IAAM,GAAG,GAAQ,EAAE,CAAA;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAChD,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;KACvB;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AARD,0BAQC;AAED,IAAI,UAAU,GAAoB,IAAI,CAAA;AACtC,SAAgB,kBAAkB,CAAC,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IACtD,IAAI,UAAU,EAAE;QACd,OAAO,UAAU,CAAA;KAClB;IAED,IAAI,OAAO,CAAC,sBAAsB,EAAE;QAClC,UAAU,GAAG,OAAO,CAAC,sBAAsB,CAAA;QAC3C,OAAO,UAAU,CAAA;KAClB;IAED,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAA;IAEvE,OAAO,UAAU,CAAA;AACnB,CAAC;AAbD,gDAaC;AAED,SAAS,EAAE,CAAC,IAAiB,EAAE,aAAqB;IAClD,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,MAAM,CAAA;IACpD,IAAM,GAAG,GAAG,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAA;IACtE,OAAO,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACpD,CAAC;AAED,SAAS,YAAY,CAAC,IAAiB;IACrC,IAAM,UAAU,GAAG,EAAE,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAA;IAChD,IAAM,WAAW,GAAG,EAAE,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAA;IAClD,OAAO,IAAI,CAAC,WAAW,GAAG,UAAU,GAAG,WAAW,CAAA;AACpD,CAAC;AAED,SAAS,aAAa,CAAC,IAAiB;IACtC,IAAM,SAAS,GAAG,EAAE,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;IAC9C,IAAM,YAAY,GAAG,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAA;IACpD,OAAO,IAAI,CAAC,YAAY,GAAG,SAAS,GAAG,YAAY,CAAA;AACrD,CAAC;AAED,SAAgB,YAAY,CAAC,UAAuB,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IACzE,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC,UAAU,CAAC,CAAA;IACvD,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,UAAU,CAAC,CAAA;IAE1D,OAAO,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAA;AAC1B,CAAC;AALD,oCAKC;AAED,SAAgB,aAAa;IAC3B,IAAI,KAAK,CAAA;IAET,IAAI,aAAa,CAAA;IACjB,IAAI;QACF,aAAa,GAAG,OAAO,CAAA;KACxB;IAAC,OAAO,CAAC,EAAE;QACV,OAAO;KACR;IAED,IAAM,GAAG,GACP,aAAa,IAAI,aAAa,CAAC,GAAG;QAChC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB;QACpC,CAAC,CAAC,IAAI,CAAA;IACV,IAAI,GAAG,EAAE;QACP,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;QACzB,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,KAAK,GAAG,CAAC,CAAA;SACV;KACF;IACD,OAAO,KAAK,IAAI,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAA;AAC9C,CAAC;AArBD,sCAqBC;AAED,4FAA4F;AAC5F,IAAM,oBAAoB,GAAG,KAAK,CAAA;AAElC,SAAgB,qBAAqB,CAAC,MAAyB;IAC7D,IACE,MAAM,CAAC,KAAK,GAAG,oBAAoB;QACnC,MAAM,CAAC,MAAM,GAAG,oBAAoB,EACpC;QACA,IACE,MAAM,CAAC,KAAK,GAAG,oBAAoB;YACnC,MAAM,CAAC,MAAM,GAAG,oBAAoB,EACpC;YACA,IAAI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE;gBAChC,MAAM,CAAC,MAAM,IAAI,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAA;gBACpD,MAAM,CAAC,KAAK,GAAG,oBAAoB,CAAA;aACpC;iBAAM;gBACL,MAAM,CAAC,KAAK,IAAI,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAA;gBACpD,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAA;aACrC;SACF;aAAM,IAAI,MAAM,CAAC,KAAK,GAAG,oBAAoB,EAAE;YAC9C,MAAM,CAAC,MAAM,IAAI,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAA;YACpD,MAAM,CAAC,KAAK,GAAG,oBAAoB,CAAA;SACpC;aAAM;YACL,MAAM,CAAC,KAAK,IAAI,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAA;YACpD,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAA;SACrC;KACF;AACH,CAAC;AAxBD,sDAwBC;AAED,SAAgB,YAAY,CAC1B,MAAyB,EACzB,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAErB,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;YACzB,MAAM,CAAC,MAAM,CACX,OAAO,EACP,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EACzC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CACtC,CAAA;QACH,CAAC,CAAC,CAAA;KACH;IAED,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;QACzB,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAC9B,MAAM;aACH,SAAS,CACR,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EACvC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAC9C;aACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACjB,CAAA;QACD,IAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAA;QAC/B,IAAM,WAAW,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAA;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;YAC/B,WAAW,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;SAC5C;QAED,OAAO,CACL,IAAI,IAAI,CAAC,CAAC,WAAW,CAAC,EAAE;YACtB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;SAChD,CAAC,CACH,CAAA;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AApCD,oCAoCC;AAED,SAAgB,WAAW,CAAC,GAAW;IACrC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QACjC,IAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAA;QACvB,GAAG,CAAC,MAAM,GAAG;YACX,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC;gBAChB,qBAAqB,CAAC,cAAM,OAAA,OAAO,CAAC,GAAG,CAAC,EAAZ,CAAY,CAAC,CAAA;YAC3C,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QACD,GAAG,CAAC,OAAO,GAAG,MAAM,CAAA;QACpB,GAAG,CAAC,WAAW,GAAG,WAAW,CAAA;QAC7B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAA;QACtB,GAAG,CAAC,GAAG,GAAG,GAAG,CAAA;IACf,CAAC,CAAC,CAAA;AACJ,CAAC;AAbD,kCAaC;AAED,SAAsB,YAAY,CAAC,GAAe;;;YAChD,sBAAO,OAAO,CAAC,OAAO,EAAE;qBACrB,IAAI,CAAC,cAAM,OAAA,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAA1C,CAA0C,CAAC;qBACtD,IAAI,CAAC,kBAAkB,CAAC;qBACxB,IAAI,CAAC,UAAC,IAAI,IAAK,OAAA,2CAAoC,IAAI,CAAE,EAA1C,CAA0C,CAAC,EAAA;;;CAC9D;AALD,oCAKC;AAED,SAAsB,aAAa,CACjC,IAAiB,EACjB,KAAa,EACb,MAAc;;;;YAER,KAAK,GAAG,4BAA4B,CAAA;YACpC,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC5C,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;YAEtE,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,UAAG,KAAK,CAAE,CAAC,CAAA;YACrC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAG,MAAM,CAAE,CAAC,CAAA;YACvC,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,cAAO,KAAK,cAAI,MAAM,CAAE,CAAC,CAAA;YAErD,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAC3C,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;YAC5C,aAAa,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;YACpC,aAAa,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;YACpC,aAAa,CAAC,YAAY,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAA;YAE/D,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;YAC9B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YAC/B,sBAAO,YAAY,CAAC,GAAG,CAAC,EAAA;;;CACzB;AAtBD,sCAsBC;AAEM,IAAM,mBAAmB,GAAG,UAGjC,IAA6C,EAC7C,QAAW;IAEX,IAAI,IAAI,YAAY,QAAQ;QAAE,OAAO,IAAI,CAAA;IAEzC,IAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;IAEjD,IAAI,aAAa,KAAK,IAAI;QAAE,OAAO,KAAK,CAAA;IAExC,OAAO,CACL,aAAa,CAAC,WAAW,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;QAChD,IAAA,2BAAmB,EAAC,aAAa,EAAE,QAAQ,CAAC,CAC7C,CAAA;AACH,CAAC,CAAA;AAhBY,QAAA,mBAAmB,uBAgB/B"}
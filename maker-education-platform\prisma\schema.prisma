// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// 用户模型
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  name      String
  avatar    String?
  bio       String?
  role      Role     @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  posts         Post[]
  comments      Comment[]
  applications  Application[]
  teamMembers   TeamMember[]
  projects      Project[]
  projectLikes  ProjectLike[]
  projectViews  ProjectView[]
  assignedSchedules Schedule[] @relation("ScheduleAssignee")
  createdSchedules  Schedule[] @relation("ScheduleCreator")
  createdTemplates  ScheduleTemplate[] @relation("TemplateCreator")

  @@map("users")
}

// 用户角色枚举
enum Role {
  USER
  ADMIN
  MODERATOR
}

// 团队成员模型
model TeamMember {
  id          String   @id @default(cuid())
  userId      String?
  name        String
  position    String
  department  String?
  avatar      String?
  bio         String?
  skills      String   // 技能标签 (JSON字符串)
  joinDate    DateTime @default(now())
  isActive    Boolean  @default(true)
  displayOrder Int     @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  user User? @relation(fields: [userId], references: [id])

  @@map("team_members")
}

// 项目/作品模型
model Project {
  id          String      @id @default(cuid())
  title       String
  description String
  content     String?     // 详细内容
  images      String      // 图片URL数组 (JSON字符串)
  category    Category
  tags        String      // 标签 (JSON字符串)
  status      ProjectStatus @default(DRAFT)
  featured    Boolean     @default(false)
  viewCount   Int         @default(0)
  likeCount   Int         @default(0)
  shareCount  Int         @default(0)
  authorId    String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // 关联关系
  author   User      @relation(fields: [authorId], references: [id])
  comments Comment[]
  likes    ProjectLike[]
  views    ProjectView[]

  @@map("projects")
}

// 项目分类枚举
enum Category {
  STEAM_EDUCATION    // STEAM教育
  MAKER_SPACE       // 创客空间
  THREE_D_PRINTING  // 3D打印
  ROBOTICS          // 机器人
  PROGRAMMING       // 编程
  ELECTRONICS       // 电子
  CRAFTS            // 手工制作
  OTHER             // 其他
}

// 项目状态枚举
enum ProjectStatus {
  DRAFT      // 草稿
  PUBLISHED  // 已发布
  ARCHIVED   // 已归档
}

// 申请模型
model Application {
  id          String            @id @default(cuid())
  applicantId String
  name        String
  email       String
  phone       String?
  position    String            // 申请职位
  skills      String            // 技能 (JSON字符串)
  experience  String?           // 经验描述
  motivation  String            // 申请动机
  resume      String?           // 简历文件URL
  status      ApplicationStatus @default(PENDING)
  reviewNote  String?           // 审核备注
  reviewedBy  String?           // 审核人ID
  reviewedAt  DateTime?         // 审核时间
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // 关联关系
  applicant User @relation(fields: [applicantId], references: [id])

  @@map("applications")
}

// 申请状态枚举
enum ApplicationStatus {
  PENDING   // 待审核
  APPROVED  // 已通过
  REJECTED  // 已拒绝
}

// 博客文章模型
model Post {
  id        String   @id @default(cuid())
  title     String
  content   String
  excerpt   String?
  slug      String   @unique
  published Boolean  @default(false)
  featured  Boolean  @default(false)
  tags      String   // 标签 (JSON字符串)
  viewCount Int      @default(0)
  authorId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  author   User      @relation(fields: [authorId], references: [id])
  comments Comment[]

  @@map("posts")
}

// 评论模型
model Comment {
  id        String   @id @default(cuid())
  content   String
  authorId  String
  postId    String?
  projectId String?
  parentId  String?  // 父评论ID，用于回复
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  author   User      @relation(fields: [authorId], references: [id])
  post     Post?     @relation(fields: [postId], references: [id])
  project  Project?  @relation(fields: [projectId], references: [id])
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")

  @@map("comments")
}

// 排班表模型
model Schedule {
  id          String   @id @default(cuid())
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime
  location    String?
  type        String   // 'CLASS', 'MEETING', 'EVENT', 'MAINTENANCE'
  status      String   @default("ACTIVE") // 'ACTIVE', 'CANCELLED', 'COMPLETED'
  assignedTo  String?
  assignee    User?    @relation("ScheduleAssignee", fields: [assignedTo], references: [id], onDelete: SetNull)
  color       String   @default("#3B82F6") // 用于日历显示的颜色
  isRecurring Boolean  @default(false)
  recurringPattern String? // JSON string for recurring rules
  createdBy   String
  creator     User     @relation("ScheduleCreator", fields: [createdBy], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("schedules")
}

// 排班模板模型
model ScheduleTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  timeSlots   String   // JSON string for time slots
  rules       String?  // JSON string for scheduling rules
  isActive    Boolean  @default(true)
  createdBy   String
  creator     User     @relation("TemplateCreator", fields: [createdBy], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("schedule_templates")
}

// 系统设置模型
model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  type        String   @default("STRING") // STRING, NUMBER, BOOLEAN, JSON
  description String?
  category    String   @default("GENERAL") // GENERAL, CONTACT, FEATURES, APPEARANCE
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_settings")
}

// 项目点赞模型
model ProjectLike {
  id        String   @id @default(cuid())
  userId    String
  projectId String
  createdAt DateTime @default(now())

  // 关联关系
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 确保用户对同一项目只能点赞一次
  @@unique([userId, projectId])
  @@map("project_likes")
}

// 项目浏览记录模型
model ProjectView {
  id        String   @id @default(cuid())
  userId    String?  // 可选，支持匿名浏览
  projectId String
  ipAddress String?  // 用于匿名用户去重
  userAgent String?  // 浏览器信息
  createdAt DateTime @default(now())

  // 关联关系
  user    User?   @relation(fields: [userId], references: [id], onDelete: SetNull)
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("project_views")
}

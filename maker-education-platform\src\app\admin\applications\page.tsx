'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { Search, Filter, CheckCircle, XCircle, Clock, Eye, User, Mail, Phone } from 'lucide-react'

interface Application {
  id: string
  name: string
  email: string
  phone?: string
  position: string
  skills: string[]
  experience?: string
  motivation: string
  resume?: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  reviewNote?: string
  createdAt: string
  applicant: {
    id: string
    name: string
    avatar?: string
  }
}

export default function ApplicationsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [applications, setApplications] = useState<Application[]>([])
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [reviewNote, setReviewNote] = useState('')

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchApplications()
  }, [session, status, router])

  useEffect(() => {
    filterApplications()
  }, [applications, searchTerm, statusFilter])

  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/applications')
      if (response.ok) {
        const data = await response.json()
        setApplications(data.applications || [])
      }
    } catch (error) {
      console.error('Failed to fetch applications:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterApplications = () => {
    let filtered = applications

    if (searchTerm) {
      filtered = filtered.filter(app =>
        app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.position.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(app => app.status === statusFilter)
    }

    setFilteredApplications(filtered)
  }

  const handleReview = async (applicationId: string, status: 'APPROVED' | 'REJECTED') => {
    try {
      const response = await fetch(`/api/admin/applications/${applicationId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          status, 
          reviewNote: reviewNote || undefined 
        })
      })

      if (response.ok) {
        fetchApplications()
        setSelectedApplication(null)
        setReviewNote('')
      }
    } catch (error) {
      console.error('Failed to review application:', error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="w-4 h-4 text-orange-500" />
      case 'APPROVED':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'REJECTED':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return null
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return '待审核'
      case 'APPROVED': return '已通过'
      case 'REJECTED': return '已拒绝'
      default: return status
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">申请审批</h2>
            <p className="text-gray-600">审核团队加入申请</p>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-enhanced p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="搜索申请..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              {['ALL', 'PENDING', 'APPROVED', 'REJECTED'].map((status) => (
                <Button
                  key={status}
                  variant={statusFilter === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setStatusFilter(status)}
                  className="rounded-xl"
                >
                  {status === 'ALL' ? '全部' : getStatusText(status)}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Applications Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredApplications.map((application) => (
            <div key={application.id} className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {application.name.charAt(0)}
                    </span>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg font-semibold text-gray-900">{application.name}</h3>
                    <p className="text-sm text-gray-500">{application.position}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  {getStatusIcon(application.status)}
                  <span className="ml-1 text-sm font-medium">
                    {getStatusText(application.status)}
                  </span>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Mail className="w-4 h-4 mr-2" />
                  {application.email}
                </div>
                {application.phone && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="w-4 h-4 mr-2" />
                    {application.phone}
                  </div>
                )}
              </div>

              <div className="mb-4">
                <p className="text-sm text-gray-600 line-clamp-3">
                  {application.motivation}
                </p>
              </div>

              <div className="flex flex-wrap gap-1 mb-4">
                {application.skills.slice(0, 3).map((skill, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs"
                  >
                    {skill}
                  </span>
                ))}
                {application.skills.length > 3 && (
                  <span className="text-gray-400 text-xs px-2 py-1">
                    +{application.skills.length - 3}
                  </span>
                )}
              </div>

              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  {new Date(application.createdAt).toLocaleDateString('zh-CN')}
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSelectedApplication(application)}
                  className="rounded-lg"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  查看详情
                </Button>
              </div>
            </div>
          ))}
        </div>

        {filteredApplications.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">暂无申请数据</p>
          </div>
        )}

        {/* Application Detail Modal */}
        {selectedApplication && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-start mb-6">
                  <h3 className="text-xl font-bold text-gray-900">申请详情</h3>
                  <button
                    onClick={() => setSelectedApplication(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircle className="w-6 h-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">申请人</label>
                    <p className="text-gray-900">{selectedApplication.name}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">申请职位</label>
                    <p className="text-gray-900">{selectedApplication.position}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">技能专长</label>
                    <div className="flex flex-wrap gap-2">
                      {selectedApplication.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-sm"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">申请动机</label>
                    <p className="text-gray-900 whitespace-pre-wrap">{selectedApplication.motivation}</p>
                  </div>

                  {selectedApplication.experience && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">相关经验</label>
                      <p className="text-gray-900 whitespace-pre-wrap">{selectedApplication.experience}</p>
                    </div>
                  )}

                  {selectedApplication.status === 'PENDING' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">审核备注</label>
                      <textarea
                        value={reviewNote}
                        onChange={(e) => setReviewNote(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows={3}
                        placeholder="可选：添加审核备注..."
                      />
                    </div>
                  )}
                </div>

                {selectedApplication.status === 'PENDING' && (
                  <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
                    <Button
                      variant="outline"
                      onClick={() => handleReview(selectedApplication.id, 'REJECTED')}
                      className="rounded-lg"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      拒绝
                    </Button>
                    <Button
                      onClick={() => handleReview(selectedApplication.id, 'APPROVED')}
                      className="rounded-lg bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      通过
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

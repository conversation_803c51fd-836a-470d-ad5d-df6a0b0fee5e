'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import AdminLayout from '@/components/layout/admin-layout'
import { 
  Users, 
  FileText, 
  UserCheck, 
  Settings, 
  BarChart3, 
  Eye,
  Plus,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface DashboardStats {
  totalUsers: number
  totalProjects: number
  pendingApplications: number
  totalTeamMembers: number
  totalPosts: number
  publishedPosts: number
  recentProjects: any[]
  recentApplications: any[]
  recentPosts: any[]
  todayStats: {
    newUsers: number
    newProjects: number
    newApplications: number
  }
  growthRates: {
    users: number
    projects: number
    applications: number
  }
}

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return

    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }

    fetchDashboardStats()
  }, [session, status, router])

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">总用户数</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats?.totalUsers || 0}
                  </p>
                </div>
              </div>
              {stats?.todayStats?.newUsers > 0 && (
                <div className="text-right">
                  <p className="text-xs text-green-600">今日新增</p>
                  <p className="text-sm font-semibold text-green-600">
                    +{stats.todayStats.newUsers}
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-xl">
                  <FileText className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">总作品数</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats?.totalProjects || 0}
                  </p>
                </div>
              </div>
              {stats?.todayStats?.newProjects > 0 && (
                <div className="text-right">
                  <p className="text-xs text-green-600">今日新增</p>
                  <p className="text-sm font-semibold text-green-600">
                    +{stats.todayStats.newProjects}
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-3 bg-orange-100 rounded-xl">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">待审核申请</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats?.pendingApplications || 0}
                  </p>
                </div>
              </div>
              {stats?.todayStats?.newApplications > 0 && (
                <div className="text-right">
                  <p className="text-xs text-orange-600">今日新增</p>
                  <p className="text-sm font-semibold text-orange-600">
                    +{stats.todayStats.newApplications}
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center">
              <div className="p-3 bg-indigo-100 rounded-xl">
                <BookOpen className="h-6 w-6 text-indigo-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">发布文章</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.publishedPosts || 0}
                </p>
                <p className="text-xs text-gray-500">
                  总计 {stats?.totalPosts || 0} 篇
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-xl">
                <UserCheck className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">团队成员</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.totalTeamMembers || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">快速操作</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <Link href="/admin/posts/new">
                <Button className="w-full justify-start">
                  <Plus className="w-4 h-4 mr-2" />
                  新建文章
                </Button>
              </Link>
              <Link href="/admin/projects/new">
                <Button className="w-full justify-start" variant="outline">
                  <Plus className="w-4 h-4 mr-2" />
                  新建项目
                </Button>
              </Link>
              <Link href="/admin/team-members/new">
                <Button className="w-full justify-start" variant="outline">
                  <Plus className="w-4 h-4 mr-2" />
                  新建团队成员
                </Button>
              </Link>
              <Link href="/admin/applications">
                <Button className="w-full justify-start" variant="outline">
                  <Eye className="w-4 h-4 mr-2" />
                  查看申请
                </Button>
              </Link>
              <Link href="/admin/schedule/auto">
                <Button className="w-full justify-start" variant="outline">
                  <Clock className="w-4 h-4 mr-2" />
                  自动排班
                </Button>
              </Link>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Projects */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900">最新作品</h2>
                <Link href="/admin/projects">
                  <Button variant="outline" size="sm">
                    查看全部
                  </Button>
                </Link>
              </div>
            </div>
            <div className="p-6">
              {stats?.recentProjects?.length ? (
                <div className="space-y-4">
                  {stats.recentProjects.slice(0, 5).map((project: any) => (
                    <div key={project.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{project.title}</p>
                        <p className="text-sm text-gray-500">
                          by {project.author.name}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          {project.viewCount} 浏览
                        </span>
                        <Link href={`/admin/projects/${project.id}`}>
                          <Button size="sm" variant="outline">
                            编辑
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">暂无作品</p>
              )}
            </div>
          </div>

          {/* Recent Applications */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900">最新申请</h2>
                <Link href="/admin/applications">
                  <Button variant="outline" size="sm">
                    查看全部
                  </Button>
                </Link>
              </div>
            </div>
            <div className="p-6">
              {stats?.recentApplications?.length ? (
                <div className="space-y-4">
                  {stats.recentApplications.slice(0, 5).map((application: any) => (
                    <div key={application.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{application.name}</p>
                        <p className="text-sm text-gray-500">
                          申请 {application.position}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {application.status === 'PENDING' && (
                          <Clock className="w-4 h-4 text-orange-500" />
                        )}
                        {application.status === 'APPROVED' && (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                        {application.status === 'REJECTED' && (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                        <Link href={`/admin/applications/${application.id}`}>
                          <Button size="sm" variant="outline">
                            查看
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">暂无申请</p>
              )}
            </div>
          </div>
        </div>

        {/* Navigation Menu */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">管理功能</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Link href="/admin/projects" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center">
                  <FileText className="w-6 h-6 text-blue-600 mr-3" />
                  <div>
                    <h3 className="font-medium text-gray-900">作品管理</h3>
                    <p className="text-sm text-gray-500">管理所有作品内容</p>
                  </div>
                </div>
              </Link>

              <Link href="/admin/team-members" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center">
                  <UserCheck className="w-6 h-6 text-green-600 mr-3" />
                  <div>
                    <h3 className="font-medium text-gray-900">团队管理</h3>
                    <p className="text-sm text-gray-500">管理团队成员信息</p>
                  </div>
                </div>
              </Link>

              <Link href="/admin/applications" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center">
                  <Clock className="w-6 h-6 text-orange-600 mr-3" />
                  <div>
                    <h3 className="font-medium text-gray-900">申请审批</h3>
                    <p className="text-sm text-gray-500">处理加入申请</p>
                  </div>
                </div>
              </Link>

              <Link href="/admin/users" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center">
                  <Users className="w-6 h-6 text-purple-600 mr-3" />
                  <div>
                    <h3 className="font-medium text-gray-900">用户管理</h3>
                    <p className="text-sm text-gray-500">管理所有用户</p>
                  </div>
                </div>
              </Link>

              <Link href="/admin/analytics" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center">
                  <BarChart3 className="w-6 h-6 text-indigo-600 mr-3" />
                  <div>
                    <h3 className="font-medium text-gray-900">数据分析</h3>
                    <p className="text-sm text-gray-500">查看平台数据</p>
                  </div>
                </div>
              </Link>

              <Link href="/admin/settings" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center">
                  <Settings className="w-6 h-6 text-gray-600 mr-3" />
                  <div>
                    <h3 className="font-medium text-gray-900">系统设置</h3>
                    <p className="text-sm text-gray-500">配置系统参数</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { Search, Plus, Edit, Trash2, Eye, Star, Calendar } from 'lucide-react'

interface Project {
  id: string
  title: string
  description: string
  images: string[]
  category: string
  tags: string[]
  status: string
  featured: boolean
  viewCount: number
  likeCount: number
  createdAt: string
  author: {
    name: string
    avatar?: string
  }
}

const categories = {
  'STEAM_EDUCATION': 'STEAM教育',
  'THREE_D_PRINTING': '3D打印',
  'ROBOTICS': '机器人',
  'PROGRAMMING': '编程',
  'ELECTRONICS': '电子',
  'CRAFTS': '手工制作',
  'OTHER': '其他',
}

export default function ProjectsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('ALL')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchProjects()
  }, [session, status, router])

  useEffect(() => {
    filterProjects()
  }, [projects, searchTerm, categoryFilter])

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (response.ok) {
        const data = await response.json()
        setProjects(data.projects || [])
      }
    } catch (error) {
      console.error('Failed to fetch projects:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterProjects = () => {
    let filtered = projects

    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (categoryFilter !== 'ALL') {
      filtered = filtered.filter(project => project.category === categoryFilter)
    }

    setFilteredProjects(filtered)
  }

  const toggleFeatured = async (projectId: string, featured: boolean) => {
    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ featured: !featured })
      })

      if (response.ok) {
        fetchProjects()
      }
    } catch (error) {
      console.error('Failed to update project:', error)
    }
  }

  const deleteProject = async (projectId: string) => {
    if (!confirm('确定要删除这个作品吗？')) return

    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchProjects()
      }
    } catch (error) {
      console.error('Failed to delete project:', error)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">作品管理</h2>
            <p className="text-gray-600">管理学生作品展示</p>
          </div>
          <Button className="btn-enhanced rounded-xl">
            <Plus className="w-4 h-4 mr-2" />
            添加作品
          </Button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-enhanced p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="搜索作品..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button
                variant={categoryFilter === 'ALL' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCategoryFilter('ALL')}
                className="rounded-xl"
              >
                全部
              </Button>
              {Object.entries(categories).map(([key, label]) => (
                <Button
                  key={key}
                  variant={categoryFilter === key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setCategoryFilter(key)}
                  className="rounded-xl"
                >
                  {label}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <div key={project.id} className="bg-white rounded-xl shadow-enhanced overflow-hidden card-hover">
              {/* Project Image */}
              <div className="aspect-video bg-gray-200 relative">
                {project.images.length > 0 ? (
                  <img
                    src={project.images[0]}
                    alt={project.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    暂无图片
                  </div>
                )}
                <div className="absolute top-2 right-2 flex gap-2">
                  <span className="bg-blue-600 text-white px-2 py-1 rounded-lg text-xs">
                    {categories[project.category as keyof typeof categories] || project.category}
                  </span>
                  {project.featured && (
                    <span className="bg-yellow-500 text-white px-2 py-1 rounded-lg text-xs flex items-center">
                      <Star className="w-3 h-3 mr-1" />
                      精选
                    </span>
                  )}
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                  {project.title}
                </h3>
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                  {project.description}
                </p>

                <div className="flex flex-wrap gap-1 mb-4">
                  {project.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs"
                    >
                      {tag}
                    </span>
                  ))}
                  {project.tags.length > 3 && (
                    <span className="text-gray-400 text-xs px-2 py-1">
                      +{project.tags.length - 3}
                    </span>
                  )}
                </div>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(project.createdAt).toLocaleDateString('zh-CN')}
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      <Eye className="w-4 h-4 mr-1" />
                      {project.viewCount}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    by {project.author.name}
                  </span>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => toggleFeatured(project.id, project.featured)}
                      className="rounded-lg"
                    >
                      <Star className={`w-4 h-4 ${project.featured ? 'fill-current text-yellow-500' : ''}`} />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="rounded-lg"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => deleteProject(project.id)}
                      className="rounded-lg text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">暂无作品</p>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

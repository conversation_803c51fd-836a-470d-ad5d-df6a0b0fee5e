'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Zap, Settings, Calendar, Users, Clock } from 'lucide-react'
import Link from 'next/link'

interface AutoScheduleRule {
  id: string
  name: string
  description: string
  timeSlots: Array<{
    day: number // 0-6 (Sunday-Saturday)
    startTime: string
    endTime: string
    type: string
  }>
  assignmentRules: {
    balanceWorkload: boolean
    respectPreferences: boolean
    avoidConflicts: boolean
    maxHoursPerDay: number
    maxHoursPerWeek: number
  }
}

interface TeamMember {
  id: string
  name: string
  position: string
  skills: string[]
  preferences: {
    availableDays: number[]
    preferredTimeSlots: string[]
    maxHoursPerWeek: number
  }
}

export default function AutoSchedulePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [scheduleRules, setScheduleRules] = useState<AutoScheduleRule>({
    id: '',
    name: '默认排班规则',
    description: '自动排班的默认规则',
    timeSlots: [
      { day: 1, startTime: '09:00', endTime: '12:00', type: 'CLASS' },
      { day: 1, startTime: '14:00', endTime: '17:00', type: 'CLASS' },
      { day: 3, startTime: '09:00', endTime: '12:00', type: 'CLASS' },
      { day: 3, startTime: '14:00', endTime: '17:00', type: 'CLASS' },
      { day: 5, startTime: '09:00', endTime: '12:00', type: 'CLASS' },
    ],
    assignmentRules: {
      balanceWorkload: true,
      respectPreferences: true,
      avoidConflicts: true,
      maxHoursPerDay: 8,
      maxHoursPerWeek: 40
    }
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedSchedules, setGeneratedSchedules] = useState<any[]>([])

  const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchTeamMembers()
  }, [session, status, router])

  const fetchTeamMembers = async () => {
    try {
      const response = await fetch('/api/team-members')
      if (response.ok) {
        const data = await response.json()
        // 模拟偏好设置
        const membersWithPreferences = data.map((member: any) => ({
          ...member,
          skills: typeof member.skills === 'string' ? JSON.parse(member.skills) : member.skills || [],
          preferences: {
            availableDays: [1, 2, 3, 4, 5], // 工作日
            preferredTimeSlots: ['09:00-12:00', '14:00-17:00'],
            maxHoursPerWeek: 40
          }
        }))
        setTeamMembers(membersWithPreferences)
      }
    } catch (error) {
      console.error('Failed to fetch team members:', error)
    }
  }

  const generateAutoSchedule = async () => {
    setIsGenerating(true)
    try {
      // 模拟自动排班算法
      const schedules = []
      const memberWorkload: { [key: string]: number } = {}

      // 初始化工作量统计
      teamMembers.forEach(member => {
        memberWorkload[member.id] = 0
      })

      // 为每个时间段分配人员
      for (const timeSlot of scheduleRules.timeSlots) {
        // 筛选可用的团队成员
        const availableMembers = teamMembers.filter(member => 
          member.preferences.availableDays.includes(timeSlot.day) &&
          memberWorkload[member.id] < scheduleRules.assignmentRules.maxHoursPerWeek
        )

        if (availableMembers.length > 0) {
          // 选择工作量最少的成员
          const selectedMember = availableMembers.reduce((prev, current) => 
            memberWorkload[prev.id] < memberWorkload[current.id] ? prev : current
          )

          const duration = calculateDuration(timeSlot.startTime, timeSlot.endTime)
          memberWorkload[selectedMember.id] += duration

          // 生成排班记录
          const scheduleDate = getNextDateForDay(timeSlot.day)
          schedules.push({
            title: `${timeSlot.type === 'CLASS' ? '课程教学' : '其他活动'}`,
            description: `自动生成的${timeSlot.type}排班`,
            startTime: `${scheduleDate}T${timeSlot.startTime}:00`,
            endTime: `${scheduleDate}T${timeSlot.endTime}:00`,
            type: timeSlot.type,
            assignedTo: selectedMember.id,
            assigneeName: selectedMember.name,
            location: '创客教室',
            color: getColorForType(timeSlot.type)
          })
        }
      }

      setGeneratedSchedules(schedules)
    } catch (error) {
      console.error('Failed to generate schedule:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(`2000-01-01T${startTime}:00`)
    const end = new Date(`2000-01-01T${endTime}:00`)
    return (end.getTime() - start.getTime()) / (1000 * 60 * 60) // 小时
  }

  const getNextDateForDay = (dayOfWeek: number) => {
    const today = new Date()
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
    const daysUntilTarget = (dayOfWeek - nextWeek.getDay() + 7) % 7
    const targetDate = new Date(nextWeek.getTime() + daysUntilTarget * 24 * 60 * 60 * 1000)
    return targetDate.toISOString().split('T')[0]
  }

  const getColorForType = (type: string) => {
    const colors = {
      'CLASS': '#3B82F6',
      'MEETING': '#10B981',
      'EVENT': '#8B5CF6',
      'MAINTENANCE': '#F59E0B'
    }
    return colors[type as keyof typeof colors] || '#6B7280'
  }

  const saveGeneratedSchedules = async () => {
    try {
      for (const schedule of generatedSchedules) {
        await fetch('/api/admin/schedules', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(schedule)
        })
      }
      alert('排班保存成功！')
      router.push('/admin/schedule')
    } catch (error) {
      console.error('Failed to save schedules:', error)
      alert('保存失败，请重试')
    }
  }

  if (status === 'loading') {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/schedule">
              <Button variant="outline" size="sm" className="rounded-xl">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回
              </Button>
            </Link>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">自动排班</h2>
              <p className="text-gray-600">基于规则自动生成排班表</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 排班规则配置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              排班规则
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  时间段设置
                </label>
                <div className="space-y-2">
                  {scheduleRules.timeSlots.map((slot, index) => (
                    <div key={index} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <span className="text-sm font-medium">{dayNames[slot.day]}</span>
                      <span className="text-sm">{slot.startTime} - {slot.endTime}</span>
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {slot.type}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  分配规则
                </label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">工作量平衡</span>
                    <input
                      type="checkbox"
                      checked={scheduleRules.assignmentRules.balanceWorkload}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      readOnly
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">尊重个人偏好</span>
                    <input
                      type="checkbox"
                      checked={scheduleRules.assignmentRules.respectPreferences}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      readOnly
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">避免时间冲突</span>
                    <input
                      type="checkbox"
                      checked={scheduleRules.assignmentRules.avoidConflicts}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      readOnly
                    />
                  </div>
                </div>
              </div>

              <Button
                onClick={generateAutoSchedule}
                disabled={isGenerating}
                className="w-full btn-enhanced rounded-xl"
              >
                <Zap className="w-4 h-4 mr-2" />
                {isGenerating ? '生成中...' : '生成排班表'}
              </Button>
            </div>
          </div>

          {/* 团队成员状态 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Users className="w-5 h-5 mr-2" />
              团队成员 ({teamMembers.length})
            </h3>

            <div className="space-y-3">
              {teamMembers.map((member) => (
                <div key={member.id} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{member.name}</span>
                    <span className="text-sm text-gray-500">{member.position}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {member.skills.slice(0, 3).map((skill, index) => (
                      <span
                        key={index}
                        className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 生成的排班表 */}
        {generatedSchedules.length > 0 && (
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                生成的排班表 ({generatedSchedules.length} 项)
              </h3>
              <Button
                onClick={saveGeneratedSchedules}
                className="btn-enhanced rounded-xl"
              >
                保存排班表
              </Button>
            </div>

            <div className="space-y-3">
              {generatedSchedules.map((schedule, index) => (
                <div key={index} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{schedule.title}</h4>
                    <span className="text-sm text-gray-500">{schedule.assigneeName}</span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {new Date(schedule.startTime).toLocaleString('zh-CN')}
                    </div>
                    <span>-</span>
                    <span>{new Date(schedule.endTime).toLocaleString('zh-CN')}</span>
                  </div>
                  {schedule.location && (
                    <div className="text-sm text-gray-500 mt-1">
                      地点: {schedule.location}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

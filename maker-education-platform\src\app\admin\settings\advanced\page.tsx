'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Save, Palette, Eye, Globe, BarChart3, Settings2, Wrench, Plus, Trash2 } from 'lucide-react'
import Link from 'next/link'

export default function AdvancedSettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [settings, setSettings] = useState<Record<string, any>>({
    // 外观设置
    primary_color: '#3B82F6',
    secondary_color: '#6366F1',
    hero_background: 'gradient',
    show_animations: true,
    dark_mode_enabled: false,
    
    // 功能开关
    show_team_section: true,
    show_projects_section: true,
    show_blog_section: true,
    show_apply_section: true,
    enable_comments: true,
    enable_likes: true,
    
    // 首页内容
    hero_title: '创新思维 · 实践能力',
    hero_subtitle: 'STEAM创客教育平台',
    hero_description: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',
    about_intro: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',
    
    // 统计数据
    stats_projects: 500,
    stats_students: 1000,
    stats_satisfaction: 98,
    
    // SEO设置
    meta_keywords: 'STEAM教育,创客教育,3D打印,机器人,编程',
    meta_description: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程',

    // 设施设备
    facilities_equipment: '[]',

    // 人数统计
    total_members: 50,
    active_members: 35,
    total_projects: 120,
    completed_projects: 95,

    // 教室规格
    classroom_specs: '[]',

    // 主要设备清单
    main_equipment: '[]',

    // 法律文档
    terms_content: '',
    privacy_content: '',
    terms_last_updated: new Date().toISOString().split('T')[0],
    privacy_last_updated: new Date().toISOString().split('T')[0],

    // 联系信息
    contact_wechat: '',
    contact_qq: '',
    contact_email: '<EMAIL>',
    work_hours: '周一至周五 9:00-18:00'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [facilities, setFacilities] = useState<Array<{id: string, name: string, description: string, status: string}>>([])
  const [newFacility, setNewFacility] = useState({ name: '', description: '', status: 'available' })
  const [classroomSpecs, setClassroomSpecs] = useState<Array<{id: string, name: string, value: string, unit: string}>>([])
  const [newSpec, setNewSpec] = useState({ name: '', value: '', unit: '' })
  const [mainEquipment, setMainEquipment] = useState<Array<{id: string, name: string, quantity: number, description: string}>>([])
  const [newEquipment, setNewEquipment] = useState({ name: '', quantity: 1, description: '' })

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchSettings()
  }, [session, status, router])

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings')
      if (response.ok) {
        const data = await response.json()
        const formattedSettings = Object.keys(data).reduce((acc, key) => {
          if (settings.hasOwnProperty(key)) {
            acc[key] = data[key].value
          }
          return acc
        }, {} as any)
        setSettings(prev => ({ ...prev, ...formattedSettings }))

        // 处理设施设备数据
        if (data.facilities_equipment) {
          try {
            const facilitiesData = JSON.parse(data.facilities_equipment.value)
            setFacilities(facilitiesData)
          } catch (e) {
            setFacilities([])
          }
        }

        // 处理教室规格数据
        if (data.classroom_specs) {
          try {
            const specsData = JSON.parse(data.classroom_specs.value)
            setClassroomSpecs(specsData)
          } catch (e) {
            setClassroomSpecs([])
          }
        }

        // 处理主要设备数据
        if (data.main_equipment) {
          try {
            const equipmentData = JSON.parse(data.main_equipment.value)
            setMainEquipment(equipmentData)
          } catch (e) {
            setMainEquipment([])
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked :
              type === 'number' ? parseFloat(value) || 0 : value
    }))
  }

  const addFacility = () => {
    if (newFacility.name.trim()) {
      const facility = {
        id: Date.now().toString(),
        ...newFacility
      }
      setFacilities(prev => [...prev, facility])
      setNewFacility({ name: '', description: '', status: 'available' })
    }
  }

  const removeFacility = (id: string) => {
    setFacilities(prev => prev.filter(f => f.id !== id))
  }

  const updateFacility = (id: string, field: string, value: string) => {
    setFacilities(prev => prev.map(f =>
      f.id === id ? { ...f, [field]: value } : f
    ))
  }

  // 教室规格管理
  const addSpec = () => {
    if (newSpec.name.trim()) {
      const spec = {
        id: Date.now().toString(),
        ...newSpec
      }
      setClassroomSpecs(prev => [...prev, spec])
      setNewSpec({ name: '', value: '', unit: '' })
    }
  }

  const removeSpec = (id: string) => {
    setClassroomSpecs(prev => prev.filter(s => s.id !== id))
  }

  const updateSpec = (id: string, field: string, value: string) => {
    setClassroomSpecs(prev => prev.map(s =>
      s.id === id ? { ...s, [field]: value } : s
    ))
  }

  // 主要设备管理
  const addEquipment = () => {
    if (newEquipment.name.trim()) {
      const equipment = {
        id: Date.now().toString(),
        ...newEquipment
      }
      setMainEquipment(prev => [...prev, equipment])
      setNewEquipment({ name: '', quantity: 1, description: '' })
    }
  }

  const removeEquipment = (id: string) => {
    setMainEquipment(prev => prev.filter(e => e.id !== id))
  }

  const updateEquipment = (id: string, field: string, value: string | number) => {
    setMainEquipment(prev => prev.map(e =>
      e.id === id ? { ...e, [field]: value } : e
    ))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      // 添加所有自定义数据到设置中
      const settingsWithCustomData = {
        ...settings,
        facilities_equipment: JSON.stringify(facilities),
        classroom_specs: JSON.stringify(classroomSpecs),
        main_equipment: JSON.stringify(mainEquipment)
      }

      const formattedSettings = Object.keys(settingsWithCustomData).reduce((acc, key) => {
        const value = settingsWithCustomData[key as keyof typeof settingsWithCustomData]
        let type = 'STRING'

        if (typeof value === 'boolean') {
          type = 'BOOLEAN'
        } else if (typeof value === 'number') {
          type = 'NUMBER'
        }

        acc[key] = {
          value,
          type,
          category: getCategoryForKey(key)
        }
        return acc
      }, {} as any)

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formattedSettings)
      })

      if (response.ok) {
        setMessage('高级设置保存成功！')
      } else {
        setMessage('保存失败，请重试')
      }
      setTimeout(() => setMessage(''), 3000)
    } catch (error) {
      setMessage('保存失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  const getCategoryForKey = (key: string) => {
    if (['primary_color', 'secondary_color', 'hero_background', 'show_animations', 'dark_mode_enabled'].includes(key)) return 'APPEARANCE'
    if (['show_team_section', 'show_projects_section', 'show_blog_section', 'show_apply_section', 'enable_comments', 'enable_likes'].includes(key)) return 'FEATURES'
    if (['hero_title', 'hero_subtitle', 'hero_description', 'about_intro'].includes(key)) return 'CONTENT'
    if (['stats_projects', 'stats_students', 'stats_satisfaction'].includes(key)) return 'STATS'
    if (['meta_keywords', 'meta_description'].includes(key)) return 'SEO'
    return 'GENERAL'
  }

  if (status === 'loading') {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/settings">
              <Button variant="outline" size="sm" className="rounded-xl">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回基本设置
              </Button>
            </Link>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">高级设置</h2>
              <p className="text-gray-600">自定义网站外观、功能和内容</p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 外观设置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Palette className="w-5 h-5 mr-2" />
              外观设置
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  主题色
                </label>
                <input
                  type="color"
                  name="primary_color"
                  value={settings.primary_color}
                  onChange={handleChange}
                  className="w-full h-10 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  辅助色
                </label>
                <input
                  type="color"
                  name="secondary_color"
                  value={settings.secondary_color}
                  onChange={handleChange}
                  className="w-full h-10 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  首页背景样式
                </label>
                <select
                  name="hero_background"
                  value={settings.hero_background}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="gradient">渐变背景</option>
                  <option value="solid">纯色背景</option>
                  <option value="image">图片背景</option>
                </select>
              </div>
            </div>

            <div className="mt-6 space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">显示动画效果</label>
                  <p className="text-sm text-gray-500">启用页面动画和过渡效果</p>
                </div>
                <input
                  type="checkbox"
                  name="show_animations"
                  checked={settings.show_animations}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">启用暗色模式</label>
                  <p className="text-sm text-gray-500">提供暗色主题选项</p>
                </div>
                <input
                  type="checkbox"
                  name="dark_mode_enabled"
                  checked={settings.dark_mode_enabled}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* 功能开关 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Settings2 className="w-5 h-5 mr-2" />
              功能开关
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">显示团队介绍</label>
                    <p className="text-sm text-gray-500">在导航栏显示团队页面</p>
                  </div>
                  <input
                    type="checkbox"
                    name="show_team_section"
                    checked={settings.show_team_section}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">显示作品展示</label>
                    <p className="text-sm text-gray-500">在导航栏显示作品页面</p>
                  </div>
                  <input
                    type="checkbox"
                    name="show_projects_section"
                    checked={settings.show_projects_section}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">显示博客功能</label>
                    <p className="text-sm text-gray-500">启用博客文章功能</p>
                  </div>
                  <input
                    type="checkbox"
                    name="show_blog_section"
                    checked={settings.show_blog_section}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">显示申请功能</label>
                    <p className="text-sm text-gray-500">允许用户提交申请</p>
                  </div>
                  <input
                    type="checkbox"
                    name="show_apply_section"
                    checked={settings.show_apply_section}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">启用评论功能</label>
                    <p className="text-sm text-gray-500">允许用户发表评论</p>
                  </div>
                  <input
                    type="checkbox"
                    name="enable_comments"
                    checked={settings.enable_comments}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">启用点赞功能</label>
                    <p className="text-sm text-gray-500">允许用户点赞内容</p>
                  </div>
                  <input
                    type="checkbox"
                    name="enable_likes"
                    checked={settings.enable_likes}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 首页内容设置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Globe className="w-5 h-5 mr-2" />
              首页内容
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  首页主标题
                </label>
                <input
                  type="text"
                  name="hero_title"
                  value={settings.hero_title}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  首页副标题
                </label>
                <input
                  type="text"
                  name="hero_subtitle"
                  value={settings.hero_subtitle}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  首页描述
                </label>
                <textarea
                  name="hero_description"
                  value={settings.hero_description}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  关于我们简介
                </label>
                <textarea
                  name="about_intro"
                  value={settings.about_intro}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* 统计数据设置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              统计数据
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  学生作品数
                </label>
                <input
                  type="number"
                  name="stats_projects"
                  value={settings.stats_projects}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  培训学员数
                </label>
                <input
                  type="number"
                  name="stats_students"
                  value={settings.stats_students}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  满意度评价 (%)
                </label>
                <input
                  type="number"
                  name="stats_satisfaction"
                  value={settings.stats_satisfaction}
                  onChange={handleChange}
                  min="0"
                  max="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* 人数统计设置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              人数统计设置
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  总成员数
                </label>
                <input
                  type="number"
                  name="total_members"
                  value={settings.total_members}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  活跃成员数
                </label>
                <input
                  type="number"
                  name="active_members"
                  value={settings.active_members}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  总项目数
                </label>
                <input
                  type="number"
                  name="total_projects"
                  value={settings.total_projects}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  完成项目数
                </label>
                <input
                  type="number"
                  name="completed_projects"
                  value={settings.completed_projects}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* 设施设备管理 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Wrench className="w-5 h-5 mr-2" />
              设施设备管理
            </h3>

            {/* 添加新设施 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-xl">
              <h4 className="text-md font-medium text-gray-800 mb-3">添加新设施设备</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <input
                    type="text"
                    placeholder="设备名称"
                    value={newFacility.name}
                    onChange={(e) => setNewFacility(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="设备描述"
                    value={newFacility.description}
                    onChange={(e) => setNewFacility(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <select
                    value={newFacility.status}
                    onChange={(e) => setNewFacility(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="available">可用</option>
                    <option value="maintenance">维护中</option>
                    <option value="unavailable">不可用</option>
                  </select>
                </div>
                <div>
                  <Button
                    type="button"
                    onClick={addFacility}
                    className="w-full btn-enhanced rounded-lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    添加
                  </Button>
                </div>
              </div>
            </div>

            {/* 设施列表 */}
            <div className="space-y-3">
              {facilities.length === 0 ? (
                <p className="text-gray-500 text-center py-4">暂无设施设备，请添加新设备</p>
              ) : (
                facilities.map((facility) => (
                  <div key={facility.id} className="flex items-center gap-4 p-3 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <input
                        type="text"
                        value={facility.name}
                        onChange={(e) => updateFacility(facility.id, 'name', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={facility.description}
                        onChange={(e) => updateFacility(facility.id, 'description', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="w-32">
                      <select
                        value={facility.status}
                        onChange={(e) => updateFacility(facility.id, 'status', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="available">可用</option>
                        <option value="maintenance">维护中</option>
                        <option value="unavailable">不可用</option>
                      </select>
                    </div>
                    <Button
                      type="button"
                      onClick={() => removeFacility(facility.id)}
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 教室规格管理 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Settings2 className="w-5 h-5 mr-2" />
              教室规格管理
            </h3>

            {/* 添加新规格 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-xl">
              <h4 className="text-md font-medium text-gray-800 mb-3">添加新规格</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <input
                    type="text"
                    placeholder="规格名称"
                    value={newSpec.name}
                    onChange={(e) => setNewSpec(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="数值"
                    value={newSpec.value}
                    onChange={(e) => setNewSpec(prev => ({ ...prev, value: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="单位"
                    value={newSpec.unit}
                    onChange={(e) => setNewSpec(prev => ({ ...prev, unit: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <Button
                    type="button"
                    onClick={addSpec}
                    className="w-full btn-enhanced rounded-lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    添加
                  </Button>
                </div>
              </div>
            </div>

            {/* 规格列表 */}
            <div className="space-y-3">
              {classroomSpecs.length === 0 ? (
                <p className="text-gray-500 text-center py-4">暂无教室规格，请添加新规格</p>
              ) : (
                classroomSpecs.map((spec) => (
                  <div key={spec.id} className="flex items-center gap-4 p-3 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <input
                        type="text"
                        value={spec.name}
                        onChange={(e) => updateSpec(spec.id, 'name', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={spec.value}
                        onChange={(e) => updateSpec(spec.id, 'value', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="w-24">
                      <input
                        type="text"
                        value={spec.unit}
                        onChange={(e) => updateSpec(spec.id, 'unit', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <Button
                      type="button"
                      onClick={() => removeSpec(spec.id)}
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 主要设备清单管理 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Wrench className="w-5 h-5 mr-2" />
              主要设备清单管理
            </h3>

            {/* 添加新设备 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-xl">
              <h4 className="text-md font-medium text-gray-800 mb-3">添加新设备</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <input
                    type="text"
                    placeholder="设备名称"
                    value={newEquipment.name}
                    onChange={(e) => setNewEquipment(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <input
                    type="number"
                    placeholder="数量"
                    value={newEquipment.quantity}
                    onChange={(e) => setNewEquipment(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))}
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="设备描述"
                    value={newEquipment.description}
                    onChange={(e) => setNewEquipment(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <Button
                    type="button"
                    onClick={addEquipment}
                    className="w-full btn-enhanced rounded-lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    添加
                  </Button>
                </div>
              </div>
            </div>

            {/* 设备列表 */}
            <div className="space-y-3">
              {mainEquipment.length === 0 ? (
                <p className="text-gray-500 text-center py-4">暂无主要设备，请添加新设备</p>
              ) : (
                mainEquipment.map((equipment) => (
                  <div key={equipment.id} className="flex items-center gap-4 p-3 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <input
                        type="text"
                        value={equipment.name}
                        onChange={(e) => updateEquipment(equipment.id, 'name', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="w-20">
                      <input
                        type="number"
                        value={equipment.quantity}
                        onChange={(e) => updateEquipment(equipment.id, 'quantity', parseInt(e.target.value) || 1)}
                        min="1"
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={equipment.description}
                        onChange={(e) => updateEquipment(equipment.id, 'description', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <Button
                      type="button"
                      onClick={() => removeEquipment(equipment.id)}
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 联系信息管理 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Settings2 className="w-5 h-5 mr-2" />
              联系信息管理
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  微信号
                </label>
                <input
                  type="text"
                  name="contact_wechat"
                  value={settings.contact_wechat}
                  onChange={handleChange}
                  placeholder="请输入微信号"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  QQ号
                </label>
                <input
                  type="text"
                  name="contact_qq"
                  value={settings.contact_qq}
                  onChange={handleChange}
                  placeholder="请输入QQ号"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系邮箱
                </label>
                <input
                  type="email"
                  name="contact_email"
                  value={settings.contact_email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  工作时间
                </label>
                <input
                  type="text"
                  name="work_hours"
                  value={settings.work_hours}
                  onChange={handleChange}
                  placeholder="周一至周五 9:00-18:00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* 法律文档管理 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Settings2 className="w-5 h-5 mr-2" />
              法律文档管理
            </h3>

            {/* 使用条款 */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-medium text-gray-800">使用条款</h4>
                <div className="flex items-center space-x-4">
                  <label className="text-sm text-gray-600">最后更新日期:</label>
                  <input
                    type="date"
                    name="terms_last_updated"
                    value={settings.terms_last_updated}
                    onChange={handleChange}
                    className="px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <textarea
                name="terms_content"
                value={settings.terms_content}
                onChange={handleChange}
                rows={10}
                placeholder="请输入使用条款内容..."
                className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 隐私政策 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-medium text-gray-800">隐私政策</h4>
                <div className="flex items-center space-x-4">
                  <label className="text-sm text-gray-600">最后更新日期:</label>
                  <input
                    type="date"
                    name="privacy_last_updated"
                    value={settings.privacy_last_updated}
                    onChange={handleChange}
                    className="px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <textarea
                name="privacy_content"
                value={settings.privacy_content}
                onChange={handleChange}
                rows={10}
                placeholder="请输入隐私政策内容..."
                className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* SEO设置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Eye className="w-5 h-5 mr-2" />
              SEO设置
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO关键词
                </label>
                <input
                  type="text"
                  name="meta_keywords"
                  value={settings.meta_keywords}
                  onChange={handleChange}
                  placeholder="用逗号分隔多个关键词"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO描述
                </label>
                <textarea
                  name="meta_description"
                  value={settings.meta_description}
                  onChange={handleChange}
                  rows={3}
                  placeholder="网站的SEO描述，建议150字以内"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isLoading}
              className="btn-enhanced btn-submit rounded-xl px-8"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? '保存中...' : '保存高级设置'}
            </Button>
          </div>

          {/* 消息提示 */}
          {message && (
            <div className={`p-4 rounded-xl ${
              message.includes('成功') 
                ? 'bg-green-50 text-green-800 border border-green-200' 
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message}
            </div>
          )}
        </form>
      </div>
    </AdminLayout>
  )
}

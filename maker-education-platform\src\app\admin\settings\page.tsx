'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { Save, Database, Mail, Globe, Shield, Palette, Settings, Eye } from 'lucide-react'
import Link from 'next/link'

export default function SettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [settings, setSettings] = useState<Record<string, any>>({
    site_name: '创客教育平台',
    site_description: '专业的STEAM教育平台',
    contact_phone: '************',
    contact_email: '<EMAIL>',
    contact_address: '北京市海淀区创新大厦',
    team_count: 50,
    copyright_year: '2024',
    allow_registration: true,
    require_approval: true,
    maintenance_mode: false,
    max_file_size: 10,
    allowed_file_types: 'jpg,jpeg,png,gif,pdf,doc,docx',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchSettings()
  }, [session, status, router])

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings')
      if (response.ok) {
        const data = await response.json()
        const formattedSettings = Object.keys(data).reduce((acc, key) => {
          acc[key] = data[key].value
          return acc
        }, {} as any)
        setSettings(prev => ({ ...prev, ...formattedSettings }))
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      // 格式化设置数据
      const formattedSettings = Object.keys(settings).reduce((acc, key) => {
        const value = settings[key as keyof typeof settings]
        let type = 'STRING'

        if (typeof value === 'boolean') {
          type = 'BOOLEAN'
        } else if (typeof value === 'number') {
          type = 'NUMBER'
        }

        acc[key] = {
          value,
          type,
          category: getCategoryForKey(key)
        }
        return acc
      }, {} as any)

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formattedSettings)
      })

      if (response.ok) {
        setMessage('设置保存成功！')
      } else {
        setMessage('保存失败，请重试')
      }
      setTimeout(() => setMessage(''), 3000)
    } catch (error) {
      setMessage('保存失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  const getCategoryForKey = (key: string) => {
    if (key.startsWith('contact_')) return 'CONTACT'
    if (['allow_registration', 'require_approval', 'maintenance_mode', 'max_file_size', 'allowed_file_types'].includes(key)) return 'FEATURES'
    return 'GENERAL'
  }

  if (status === 'loading') {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">系统设置</h2>
            <p className="text-gray-600">配置平台基本设置和参数</p>
          </div>
          <div className="flex space-x-2">
            <Link href="/admin/settings/preview">
              <Button variant="outline" className="btn-enhanced rounded-xl">
                <Eye className="w-4 h-4 mr-2" />
                实时预览
              </Button>
            </Link>
            <Link href="/admin/settings/advanced">
              <Button variant="outline" className="btn-enhanced rounded-xl">
                <Settings className="w-4 h-4 mr-2" />
                高级设置
              </Button>
            </Link>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本设置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <div className="flex items-center mb-4">
              <Globe className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">基本设置</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  网站名称
                </label>
                <input
                  type="text"
                  name="site_name"
                  value={settings.site_name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系邮箱
                </label>
                <input
                  type="email"
                  name="contact_email"
                  value={settings.contact_email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系电话
                </label>
                <input
                  type="text"
                  name="contact_phone"
                  value={settings.contact_phone}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  团队人数
                </label>
                <input
                  type="number"
                  name="team_count"
                  value={settings.team_count}
                  onChange={handleChange}
                  min="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  版权年份
                </label>
                <input
                  type="text"
                  name="copyright_year"
                  value={settings.copyright_year}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                网站描述
              </label>
              <textarea
                name="site_description"
                value={settings.site_description}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                联系地址
              </label>
              <input
                type="text"
                name="contact_address"
                value={settings.contact_address}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* 用户设置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <div className="flex items-center mb-4">
              <Shield className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">用户设置</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">允许用户注册</label>
                  <p className="text-sm text-gray-500">是否允许新用户自主注册账户</p>
                </div>
                <input
                  type="checkbox"
                  name="allow_registration"
                  checked={settings.allow_registration}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">申请需要审批</label>
                  <p className="text-sm text-gray-500">关闭后申请通过将自动加入团队</p>
                </div>
                <input
                  type="checkbox"
                  name="require_approval"
                  checked={settings.require_approval}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">维护模式</label>
                  <p className="text-sm text-gray-500">开启后网站将显示维护页面</p>
                </div>
                <input
                  type="checkbox"
                  name="maintenance_mode"
                  checked={settings.maintenance_mode}
                  onChange={handleChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* 文件设置 */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <div className="flex items-center mb-4">
              <Database className="w-5 h-5 text-purple-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">文件设置</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最大文件大小 (MB)
                </label>
                <input
                  type="number"
                  name="max_file_size"
                  value={settings.max_file_size}
                  onChange={handleChange}
                  min="1"
                  max="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  允许的文件类型
                </label>
                <input
                  type="text"
                  name="allowed_file_types"
                  value={settings.allowed_file_types}
                  onChange={handleChange}
                  placeholder="jpg,png,pdf,doc"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">用逗号分隔多个文件类型</p>
              </div>
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isLoading}
              className="btn-enhanced rounded-xl"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? '保存中...' : '保存设置'}
            </Button>
          </div>

          {/* 消息提示 */}
          {message && (
            <div className={`p-4 rounded-xl ${
              message.includes('成功') 
                ? 'bg-green-50 text-green-800 border border-green-200' 
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message}
            </div>
          )}
        </form>
      </div>
    </AdminLayout>
  )
}

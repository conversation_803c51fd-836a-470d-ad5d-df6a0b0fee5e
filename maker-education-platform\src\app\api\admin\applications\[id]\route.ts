import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params
    const application = await prisma.application.findUnique({
      where: { id },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    if (!application) {
      return NextResponse.json(
        { error: '申请未找到' },
        { status: 404 }
      )
    }

    // 解析JSON字符串为数组
    const processedApplication = {
      ...application,
      skills: application.skills ? JSON.parse(application.skills) : []
    }

    return NextResponse.json(processedApplication)
  } catch (error) {
    console.error('Failed to fetch application:', error)
    return NextResponse.json(
      { error: '获取申请详情失败' },
      { status: 500 }
    )
  }
})

export const PATCH = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params
    const { status, reviewNote } = await request.json()

    if (!['APPROVED', 'REJECTED'].includes(status)) {
      return NextResponse.json(
        { error: '无效的审核状态' },
        { status: 400 }
      )
    }

    const application = await prisma.application.update({
      where: { id },
      data: {
        status,
        reviewNote,
        reviewedBy: session.user.id,
        reviewedAt: new Date()
      },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    // 如果申请通过，可以考虑自动创建团队成员记录
    if (status === 'APPROVED') {
      // 这里可以添加自动创建团队成员的逻辑
      // 或者发送通知邮件等
    }

    // 解析JSON字符串为数组
    const processedApplication = {
      ...application,
      skills: application.skills ? JSON.parse(application.skills) : []
    }

    return NextResponse.json(processedApplication)
  } catch (error) {
    console.error('Failed to review application:', error)
    return NextResponse.json(
      { error: '审核申请失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    await prisma.application.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: '申请删除成功' })
  } catch (error) {
    console.error('Failed to delete application:', error)
    return NextResponse.json(
      { error: '删除申请失败' },
      { status: 500 }
    )
  }
})

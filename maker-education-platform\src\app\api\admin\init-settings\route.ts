import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const POST = withAdmin(async (request: NextRequest, session: any) => {
  try {
    // 默认设置数据
    const defaultSettings = [
      // 基本信息
      { key: 'site_name', value: '创客教育平台', type: 'STRING', description: '网站名称' },
      { key: 'site_description', value: '专业的STEAM教育平台', type: 'STRING', description: '网站描述' },
      { key: 'site_slogan', value: '创新思维 · 实践能力', type: 'STRING', description: '网站标语' },
      { key: 'team_count', value: '50', type: 'NUMBER', description: '团队人数' },
      { key: 'copyright_year', value: '2024', type: 'STRING', description: '版权年份' },

      // 联系信息
      { key: 'contact_phone', value: '************', type: 'STRING', description: '联系电话' },
      { key: 'contact_email', value: '<EMAIL>', type: 'STRING', description: '联系邮箱' },
      { key: 'contact_address', value: '北京市海淀区创新大厦', type: 'STRING', description: '联系地址' },
      { key: 'contact_wechat', value: 'makeredu2024', type: 'STRING', description: '微信号' },
      { key: 'contact_qq', value: '123456789', type: 'STRING', description: 'QQ号' },
      { key: 'work_hours', value: '周一至周五 9:00-18:00', type: 'STRING', description: '工作时间' },

      // 功能开关
      { key: 'allow_registration', value: 'true', type: 'BOOLEAN', description: '允许注册' },
      { key: 'show_team_section', value: 'true', type: 'BOOLEAN', description: '显示团队板块' },
      { key: 'show_projects_section', value: 'true', type: 'BOOLEAN', description: '显示项目板块' },
      { key: 'show_blog_section', value: 'true', type: 'BOOLEAN', description: '显示博客板块' },
      { key: 'show_apply_section', value: 'true', type: 'BOOLEAN', description: '显示申请板块' },
      { key: 'enable_comments', value: 'true', type: 'BOOLEAN', description: '启用评论' },
      { key: 'enable_likes', value: 'true', type: 'BOOLEAN', description: '启用点赞' },

      // 外观设置
      { key: 'primary_color', value: '#3B82F6', type: 'STRING', description: '主色调' },
      { key: 'secondary_color', value: '#6366F1', type: 'STRING', description: '辅助色调' },
      { key: 'hero_background', value: 'gradient', type: 'STRING', description: '首页背景' },
      { key: 'show_animations', value: 'true', type: 'BOOLEAN', description: '显示动画' },
      { key: 'dark_mode_enabled', value: 'false', type: 'BOOLEAN', description: '启用暗色模式' },

      // 首页内容
      { key: 'hero_title', value: '创新思维 · 实践能力', type: 'STRING', description: '首页标题' },
      { key: 'hero_subtitle', value: 'STEAM创客教育平台', type: 'STRING', description: '首页副标题' },
      { key: 'hero_description', value: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。', type: 'STRING', description: '首页描述' },
      { key: 'about_intro', value: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。', type: 'STRING', description: '关于我们介绍' },

      // 统计数据
      { key: 'stats_projects', value: '500', type: 'NUMBER', description: '项目统计' },
      { key: 'stats_students', value: '1000', type: 'NUMBER', description: '学生统计' },
      { key: 'stats_satisfaction', value: '98', type: 'NUMBER', description: '满意度统计' },
      { key: 'total_members', value: '50', type: 'NUMBER', description: '总成员数' },
      { key: 'active_members', value: '35', type: 'NUMBER', description: '活跃成员数' },
      { key: 'total_projects', value: '120', type: 'NUMBER', description: '总项目数' },
      { key: 'completed_projects', value: '95', type: 'NUMBER', description: '完成项目数' },

      // SEO设置
      { key: 'meta_keywords', value: 'STEAM教育,创客教育,3D打印,机器人,编程', type: 'STRING', description: 'SEO关键词' },
      { key: 'meta_description', value: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程', type: 'STRING', description: 'SEO描述' },

      // 法律文档
      { key: 'terms_content', value: `欢迎使用创客教育平台！

1. 服务条款
通过访问和使用本平台，您同意遵守以下使用条款。如果您不同意这些条款，请不要使用本平台。

2. 用户责任
- 您必须年满13岁才能使用本平台
- 您有责任保护您的账户信息和密码
- 您同意不进行任何违法或有害的活动
- 您同意尊重其他用户和平台规则

3. 知识产权
本平台的所有内容，包括但不限于文本、图片、视频、软件等，均受知识产权法保护。未经许可，不得复制、分发或修改。

4. 隐私保护
我们重视您的隐私，详细信息请参阅我们的隐私政策。

5. 服务变更
我们保留随时修改或终止服务的权利，恕不另行通知。

6. 免责声明
本平台按"现状"提供服务，不提供任何明示或暗示的保证。

7. 联系我们
如有任何问题，请通过平台提供的联系方式与我们联系。

本条款的解释权归创客教育平台所有。`, type: 'STRING', description: '使用条款内容' },
      
      { key: 'privacy_content', value: `创客教育平台隐私政策

我们非常重视您的隐私保护。本隐私政策说明了我们如何收集、使用和保护您的个人信息。

1. 信息收集
我们可能收集以下信息：
- 注册时提供的基本信息（姓名、邮箱等）
- 使用平台时的行为数据
- 设备信息和技术数据
- 通过cookies收集的信息

2. 信息使用
我们使用收集的信息用于：
- 提供和改善我们的服务
- 与您沟通和提供客户支持
- 发送重要通知和更新
- 进行数据分析和研究

3. 信息共享
我们不会向第三方出售、交易或转让您的个人信息，除非：
- 获得您的明确同意
- 法律要求或政府部门要求
- 保护我们的权利和安全

4. 信息安全
我们采取适当的技术和组织措施来保护您的个人信息：
- 数据加密传输和存储
- 访问控制和权限管理
- 定期安全审计和更新

5. 您的权利
您有权：
- 访问和更新您的个人信息
- 删除您的账户和相关数据
- 选择退出某些数据收集
- 投诉和寻求救济

6. Cookie政策
我们使用cookies来改善用户体验。您可以通过浏览器设置管理cookies。

7. 政策更新
我们可能会不时更新本隐私政策。重大变更将通过平台通知您。

8. 联系我们
如对本隐私政策有任何疑问，请联系我们。

最后更新日期：${new Date().toLocaleDateString('zh-CN')}`, type: 'STRING', description: '隐私政策内容' },
      
      { key: 'terms_last_updated', value: new Date().toISOString().split('T')[0], type: 'STRING', description: '使用条款更新日期' },
      { key: 'privacy_last_updated', value: new Date().toISOString().split('T')[0], type: 'STRING', description: '隐私政策更新日期' },

      // 自定义数据
      { key: 'facilities_equipment', value: '[]', type: 'JSON', description: '设施设备数据' },
      { key: 'classroom_specs', value: '[]', type: 'JSON', description: '教室规格数据' },
      { key: 'main_equipment', value: '[]', type: 'JSON', description: '主要设备数据' },
    ]

    // 批量创建或更新设置
    for (const setting of defaultSettings) {
      await prisma.setting.upsert({
        where: { key: setting.key },
        update: {
          value: setting.value,
          type: setting.type,
          description: setting.description,
          updatedAt: new Date()
        },
        create: setting
      })
    }

    return NextResponse.json({ 
      message: '默认设置初始化成功',
      count: defaultSettings.length 
    })
  } catch (error) {
    console.error('Failed to initialize settings:', error)
    return NextResponse.json(
      { error: '初始化设置失败' },
      { status: 500 }
    )
  }
})

import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth-utils'
import { prisma } from '@/lib/prisma'

// 分类映射
const categoryMap: { [key: string]: string } = {
  'STEAM_EDUCATION': 'STEAM教育',
  'THREE_D_PRINTING': '3D打印',
  'ROBOTICS': '机器人',
  'PROGRAMMING': '编程',
  'ELECTRONICS': '电子',
  'CRAFTS': '手工制作',
  'MAKER_SPACE': '创客空间',
  'OTHER': '其他'
}

// 反向映射（中文到枚举值）
const reverseCategoryMap: { [key: string]: string } = {
  'STEAM教育': 'STEAM_EDUCATION',
  '3D打印': 'THREE_D_PRINTING',
  '机器人': 'ROBOTICS',
  '机器人制作': 'ROBOTICS',
  '编程': 'PROGRAMMING',
  '编程项目': 'PROGRAMMING',
  '电子': 'ELECTRONICS',
  '电子制作': 'ELECTRONICS',
  '手工制作': 'CRAFTS',
  '创意设计': 'CRAFTS',
  '创客空间': 'MAKER_SPACE',
  '其他': 'OTHER'
}

export const GET = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  
  // 只有管理员可以访问
  if (session.user.role !== 'ADMIN') {
    return NextResponse.json(
      { error: '无权限访问' },
      { status: 403 }
    )
  }

  try {
    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    if (!project) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    // 解析JSON字符串为数组，并转换分类显示名称
    const processedProject = {
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : [],
      categoryDisplay: categoryMap[project.category] || project.category
    }

    return NextResponse.json(processedProject)
  } catch (error) {
    console.error('Failed to fetch project:', error)
    return NextResponse.json(
      { error: '获取作品失败' },
      { status: 500 }
    )
  }
})

export const PUT = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  
  // 只有管理员可以访问
  if (session.user.role !== 'ADMIN') {
    return NextResponse.json(
      { error: '无权限访问' },
      { status: 403 }
    )
  }

  try {
    const {
      title,
      description,
      content,
      images,
      category,
      tags,
      featured,
      status
    } = await request.json()

    // 验证必填字段
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: '标题、描述和分类为必填项' },
        { status: 400 }
      )
    }

    // 转换分类为枚举值
    const categoryEnum = reverseCategoryMap[category] || category

    // 检查作品是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    const project = await prisma.project.update({
      where: { id },
      data: {
        title,
        description,
        content,
        images: typeof images === 'string' ? images : JSON.stringify(images || []),
        category: categoryEnum,
        tags: typeof tags === 'string' ? tags : JSON.stringify(tags || []),
        featured: featured || false,
        status: status || existingProject.status
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组返回，并转换分类显示名称
    const processedProject = {
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : [],
      categoryDisplay: categoryMap[project.category] || project.category
    }

    return NextResponse.json(processedProject)
  } catch (error) {
    console.error('Failed to update project:', error)
    return NextResponse.json(
      { error: '更新作品失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  
  // 只有管理员可以访问
  if (session.user.role !== 'ADMIN') {
    return NextResponse.json(
      { error: '无权限访问' },
      { status: 403 }
    )
  }

  try {
    // 检查作品是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    // 删除作品
    await prisma.project.delete({
      where: { id }
    })

    return NextResponse.json({ message: '作品删除成功' })
  } catch (error) {
    console.error('Failed to delete project:', error)
    return NextResponse.json(
      { error: '删除作品失败' },
      { status: 500 }
    )
  }
})

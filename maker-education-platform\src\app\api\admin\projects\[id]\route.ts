import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth-utils'
import { prisma } from '@/lib/prisma'

export const GET = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  
  // 只有管理员可以访问
  if (session.user.role !== 'ADMIN') {
    return NextResponse.json(
      { error: '无权限访问' },
      { status: 403 }
    )
  }

  try {
    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    if (!project) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    // 解析JSON字符串为数组
    const processedProject = {
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : []
    }

    return NextResponse.json(processedProject)
  } catch (error) {
    console.error('Failed to fetch project:', error)
    return NextResponse.json(
      { error: '获取作品失败' },
      { status: 500 }
    )
  }
})

export const PUT = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  
  // 只有管理员可以访问
  if (session.user.role !== 'ADMIN') {
    return NextResponse.json(
      { error: '无权限访问' },
      { status: 403 }
    )
  }

  try {
    const {
      title,
      description,
      content,
      images,
      category,
      tags,
      featured,
      status
    } = await request.json()

    // 验证必填字段
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: '标题、描述和分类为必填项' },
        { status: 400 }
      )
    }

    // 检查作品是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    const project = await prisma.project.update({
      where: { id },
      data: {
        title,
        description,
        content,
        images: typeof images === 'string' ? images : JSON.stringify(images || []),
        category,
        tags: typeof tags === 'string' ? tags : JSON.stringify(tags || []),
        featured: featured || false,
        status: status || existingProject.status
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组返回
    const processedProject = {
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : []
    }

    return NextResponse.json(processedProject)
  } catch (error) {
    console.error('Failed to update project:', error)
    return NextResponse.json(
      { error: '更新作品失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  
  // 只有管理员可以访问
  if (session.user.role !== 'ADMIN') {
    return NextResponse.json(
      { error: '无权限访问' },
      { status: 403 }
    )
  }

  try {
    // 检查作品是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    // 删除作品
    await prisma.project.delete({
      where: { id }
    })

    return NextResponse.json({ message: '作品删除成功' })
  } catch (error) {
    console.error('Failed to delete project:', error)
    return NextResponse.json(
      { error: '删除作品失败' },
      { status: 500 }
    )
  }
})

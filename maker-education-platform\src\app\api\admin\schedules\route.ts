import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const where: any = {}

    if (type && type !== 'ALL') {
      where.type = type
    }

    if (status && status !== 'ALL') {
      where.status = status
    }

    if (startDate && endDate) {
      where.startTime = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    const schedules = await prisma.schedule.findMany({
      where,
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
          }
        }
      },
      orderBy: {
        startTime: 'asc'
      }
    })

    return NextResponse.json(schedules)
  } catch (error) {
    console.error('Failed to fetch schedules:', error)
    return NextResponse.json(
      { error: '获取排班列表失败' },
      { status: 500 }
    )
  }
})

export const POST = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const {
      title,
      description,
      startTime,
      endTime,
      location,
      type,
      assignedTo,
      color,
      isRecurring,
      recurringPattern
    } = await request.json()

    if (!title || !startTime || !endTime || !type) {
      return NextResponse.json(
        { error: '标题、开始时间、结束时间和类型为必填项' },
        { status: 400 }
      )
    }

    // 检查时间冲突
    const conflictingSchedule = await prisma.schedule.findFirst({
      where: {
        assignedTo,
        status: 'ACTIVE',
        OR: [
          {
            startTime: {
              lte: new Date(startTime)
            },
            endTime: {
              gt: new Date(startTime)
            }
          },
          {
            startTime: {
              lt: new Date(endTime)
            },
            endTime: {
              gte: new Date(endTime)
            }
          }
        ]
      }
    })

    if (conflictingSchedule) {
      return NextResponse.json(
        { error: '该时间段已有其他安排' },
        { status: 400 }
      )
    }

    const schedule = await prisma.schedule.create({
      data: {
        title,
        description,
        startTime: new Date(startTime),
        endTime: new Date(endTime),
        location,
        type,
        assignedTo,
        color: color || '#3B82F6',
        isRecurring: isRecurring || false,
        recurringPattern: recurringPattern ? JSON.stringify(recurringPattern) : null,
        createdBy: session.user.id
      },
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    })

    return NextResponse.json(schedule, { status: 201 })
  } catch (error) {
    console.error('Failed to create schedule:', error)
    return NextResponse.json(
      { error: '创建排班失败' },
      { status: 500 }
    )
  }
})

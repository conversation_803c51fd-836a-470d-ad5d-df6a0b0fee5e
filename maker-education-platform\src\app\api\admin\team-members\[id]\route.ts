import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    const teamMember = await prisma.teamMember.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    if (!teamMember) {
      return NextResponse.json(
        { error: '团队成员未找到' },
        { status: 404 }
      )
    }

    // 解析JSON字符串为数组
    const processedMember = {
      ...teamMember,
      skills: teamMember.skills ? JSON.parse(teamMember.skills) : []
    }

    return NextResponse.json(processedMember)
  } catch (error) {
    console.error('Failed to fetch team member:', error)
    return NextResponse.json(
      { error: '获取团队成员失败' },
      { status: 500 }
    )
  }
})

export const PATCH = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    const { 
      name, 
      position, 
      department, 
      avatar, 
      bio, 
      skills, 
      isActive, 
      displayOrder 
    } = await request.json()

    const updateData: any = {}
    if (name !== undefined) updateData.name = name
    if (position !== undefined) updateData.position = position
    if (department !== undefined) updateData.department = department
    if (avatar !== undefined) updateData.avatar = avatar
    if (bio !== undefined) updateData.bio = bio
    if (skills !== undefined) updateData.skills = JSON.stringify(skills)
    if (isActive !== undefined) updateData.isActive = isActive
    if (displayOrder !== undefined) updateData.displayOrder = displayOrder

    const teamMember = await prisma.teamMember.update({
      where: { id: params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组
    const processedMember = {
      ...teamMember,
      skills: teamMember.skills ? JSON.parse(teamMember.skills) : []
    }

    return NextResponse.json(processedMember)
  } catch (error) {
    console.error('Failed to update team member:', error)
    return NextResponse.json(
      { error: '更新团队成员失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    await prisma.teamMember.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: '团队成员删除成功' })
  } catch (error) {
    console.error('Failed to delete team member:', error)
    return NextResponse.json(
      { error: '删除团队成员失败' },
      { status: 500 }
    )
  }
})

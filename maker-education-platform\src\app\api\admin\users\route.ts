import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '50'
    const role = searchParams.get('role')
    const status = searchParams.get('status')

    const pageSize = parseInt(limit)
    const skip = (parseInt(page) - 1) * pageSize

    const where: any = {}

    if (role && role !== 'ALL') {
      where.role = role
    }

    if (status === 'active') {
      where.isActive = true
    } else if (status === 'inactive') {
      where.isActive = false
    }

    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        avatar: true,
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize,
    })

    const total = await prisma.user.count({ where })

    return NextResponse.json(users)
  } catch (error) {
    console.error('Failed to fetch users:', error)
    return NextResponse.json(
      { error: '获取用户列表失败' },
      { status: 500 }
    )
  }
})

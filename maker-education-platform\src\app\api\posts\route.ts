import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const published = searchParams.get('published')
    const featured = searchParams.get('featured')
    const limit = searchParams.get('limit')
    const page = searchParams.get('page') || '1'

    const pageSize = limit ? parseInt(limit) : 12
    const skip = (parseInt(page) - 1) * pageSize

    const where: any = {}

    if (published === 'true') {
      where.published = true
    }

    if (featured === 'true') {
      where.featured = true
    }

    const posts = await prisma.post.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize,
    })

    // 解析JSON字符串为数组
    const processedPosts = posts.map(post => ({
      ...post,
      tags: post.tags ? JSON.parse(post.tags) : []
    }))

    const total = await prisma.post.count({ where })

    return NextResponse.json(processedPosts)
  } catch (error) {
    console.error('Failed to fetch posts:', error)
    return NextResponse.json(
      { error: '获取文章列表失败' },
      { status: 500 }
    )
  }
}

export const POST = withAuth(async (request: NextRequest, session: any) => {
  try {
    const {
      title,
      content,
      excerpt,
      slug,
      published = false,
      featured = false,
      tags
    } = await request.json()

    // 验证必填字段
    if (!title || !content || !slug) {
      return NextResponse.json(
        { error: '标题、内容和链接为必填项' },
        { status: 400 }
      )
    }

    // 检查slug是否已存在
    const existingPost = await prisma.post.findUnique({
      where: { slug }
    })

    if (existingPost) {
      return NextResponse.json(
        { error: '该链接已存在，请使用其他链接' },
        { status: 400 }
      )
    }

    const post = await prisma.post.create({
      data: {
        title,
        content,
        excerpt,
        slug,
        published,
        featured,
        tags: JSON.stringify(tags || []),
        authorId: session.user.id
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组
    const processedPost = {
      ...post,
      tags: post.tags ? JSON.parse(post.tags) : []
    }

    return NextResponse.json(processedPost, { status: 201 })
  } catch (error) {
    console.error('Failed to create post:', error)
    return NextResponse.json(
      { error: '创建文章失败' },
      { status: 500 }
    )
  }
})

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = await params

    const post = await prisma.post.findUnique({
      where: {
        slug,
        published: true
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    if (!post) {
      return NextResponse.json(
        { error: '文章未找到' },
        { status: 404 }
      )
    }

    // 增加浏览次数
    await prisma.post.update({
      where: { id: post.id },
      data: {
        viewCount: {
          increment: 1
        }
      }
    })

    // 解析JSON字符串为数组
    const processedPost = {
      ...post,
      tags: post.tags ? JSON.parse(post.tags) : []
    }

    return NextResponse.json(processedPost)
  } catch (error) {
    console.error('Failed to fetch post:', error)
    return NextResponse.json(
      { error: '获取文章失败' },
      { status: 500 }
    )
  }
}

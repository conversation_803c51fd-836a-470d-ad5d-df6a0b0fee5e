import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: projectId } = await params
    const session = await getServerSession(authOptions)
    
    // 获取客户端IP和User-Agent
    const forwarded = request.headers.get('x-forwarded-for')
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // 检查项目是否存在
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    const userId = session?.user?.id

    // 检查是否需要记录浏览
    let shouldRecord = true

    if (userId) {
      // 登录用户：检查是否在最近1小时内浏览过
      const recentView = await prisma.projectView.findFirst({
        where: {
          projectId,
          userId,
          createdAt: {
            gte: new Date(Date.now() - 60 * 60 * 1000) // 1小时前
          }
        }
      })
      shouldRecord = !recentView
    } else {
      // 匿名用户：检查IP是否在最近1小时内浏览过
      const recentView = await prisma.projectView.findFirst({
        where: {
          projectId,
          ipAddress,
          userId: null,
          createdAt: {
            gte: new Date(Date.now() - 60 * 60 * 1000) // 1小时前
          }
        }
      })
      shouldRecord = !recentView
    }

    if (shouldRecord) {
      // 记录浏览
      await prisma.projectView.create({
        data: {
          projectId,
          userId,
          ipAddress,
          userAgent
        }
      })

      // 更新浏览计数
      const updatedProject = await prisma.project.update({
        where: { id: projectId },
        data: {
          viewCount: {
            increment: 1
          }
        }
      })

      return NextResponse.json({
        viewCount: updatedProject.viewCount,
        recorded: true
      })
    } else {
      return NextResponse.json({
        viewCount: project.viewCount,
        recorded: false
      })
    }
  } catch (error) {
    console.error('记录浏览失败:', error)
    return NextResponse.json(
      { error: '操作失败，请稍后重试' },
      { status: 500 }
    )
  }
}

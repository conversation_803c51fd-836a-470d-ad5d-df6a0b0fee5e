import { NextResponse } from 'next/server'
import { safeDbOperation } from '@/lib/prisma'

// 禁用缓存，确保每次都获取最新数据
export const dynamic = 'force-dynamic'
export const revalidate = 0

export async function GET() {
  const defaultSettings = {
    // 基本信息
    site_name: '创客教育平台',
    site_description: '专业的STEAM教育平台',
    site_slogan: '创新思维 · 实践能力',
    team_count: 50,
    copyright_year: '2024',

    // 联系信息
    contact_phone: '************',
    contact_email: '<EMAIL>',
    contact_address: '北京市海淀区创新大厦',
    contact_wechat: 'makeredu2024',
    contact_qq: '123456789',
    work_hours: '周一至周五 9:00-18:00',

    // 功能开关
    allow_registration: true,
    show_team_section: true,
    show_projects_section: true,
    show_blog_section: true,
    show_apply_section: true,
    enable_comments: true,
    enable_likes: true,
    maintenance_mode: false,
    require_approval: true,

    // 外观设置
    primary_color: '#3B82F6',
    secondary_color: '#6366F1',
    hero_background: 'gradient',
    show_animations: true,
    dark_mode_enabled: false,

    // 首页内容
    hero_title: '创新思维 · 实践能力',
    hero_subtitle: 'STEAM创客教育平台',
    hero_description: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',
    about_intro: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',

    // 统计数据
    stats_projects: 500,
    stats_students: 1000,
    stats_satisfaction: 98,
    total_members: 50,
    active_members: 35,
    total_projects: 120,
    completed_projects: 95,

    // SEO设置
    meta_keywords: 'STEAM教育,创客教育,3D打印,机器人,编程',
    meta_description: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程',
    
    // 法律文档
    terms_content: '',
    privacy_content: '',
    terms_last_updated: new Date().toISOString().split('T')[0],
    privacy_last_updated: new Date().toISOString().split('T')[0],
    
    // 自定义数据
    facilities_equipment: '[]',
    classroom_specs: '[]',
    main_equipment: '[]'
  }

  const publicSettingsKeys = [
    // 基本信息
    'site_name', 'site_description', 'site_slogan', 'team_count', 'copyright_year',
    // 联系信息
    'contact_phone', 'contact_email', 'contact_address', 'contact_wechat', 'contact_qq', 'work_hours',
    // 功能开关
    'allow_registration', 'show_team_section', 'show_projects_section', 'show_blog_section',
    'show_apply_section', 'enable_comments', 'enable_likes', 'maintenance_mode', 'require_approval',
    // 外观设置
    'primary_color', 'secondary_color', 'hero_background', 'show_animations', 'dark_mode_enabled',
    // 首页内容
    'hero_title', 'hero_subtitle', 'hero_description', 'about_intro',
    // 统计数据
    'stats_projects', 'stats_students', 'stats_satisfaction', 'total_members', 'active_members',
    'total_projects', 'completed_projects',
    // SEO设置
    'meta_keywords', 'meta_description',
    // 法律文档
    'terms_content', 'privacy_content', 'terms_last_updated', 'privacy_last_updated',
    // 自定义数据
    'facilities_equipment', 'classroom_specs', 'main_equipment'
  ]

  const dbSettings = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')
    return await prisma.setting.findMany({
      where: {
        key: {
          in: publicSettingsKeys
        }
      }
    })
  }, [])

  // 转换为键值对对象
  const settings: Record<string, any> = { ...defaultSettings }

  dbSettings.forEach(setting => {
    let value = setting.value
    
    // 类型转换
    if (setting.type === 'BOOLEAN') {
      value = value === 'true'
    } else if (setting.type === 'NUMBER') {
      value = parseInt(value) || 0
    }
    
    settings[setting.key] = value
  })

  return NextResponse.json(settings, {
    headers: {
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Surrogate-Control': 'no-store'
    }
  })
}

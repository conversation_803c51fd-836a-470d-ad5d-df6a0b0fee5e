import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowRight, Users, Lightbulb, Award, BookOpen } from 'lucide-react'


async function getSettings() {
  try {
    // 使用公共设置API
    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/settings/public`, {
      cache: 'no-store'
    })

    if (response.ok) {
      const settingsData = await response.json()
      // 公共设置API直接返回键值对
      const settingsMap = settingsData

      return {
        siteName: settingsMap.site_name || '创客教育平台',
        siteDescription: settingsMap.site_description || '激发创造力，培养未来创新人才',
        heroTitle: settingsMap.hero_title || '创新思维 · 实践能力',
        heroSubtitle: settingsMap.hero_subtitle || 'STEAM创客教育平台',
        heroDescription: settingsMap.hero_description || '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',
        contactEmail: settingsMap.contact_email || '<EMAIL>',
        contactPhone: settingsMap.contact_phone || '************',
        contactAddress: settingsMap.contact_address || '北京市朝阳区创新大厦',
        teamCount: parseInt(settingsMap.team_count || '50'),
        enableRegistration: settingsMap.allow_registration === 'true',
        statsProjects: parseInt(settingsMap.stats_projects || '500'),
        statsStudents: parseInt(settingsMap.stats_students || '1200'),
        statsSatisfaction: parseInt(settingsMap.stats_satisfaction || '98')
      }
    }
  } catch (error) {
    console.error('Failed to fetch settings:', error)
  }

  // 返回默认值
  return {
    siteName: '创客教育平台',
    siteDescription: '激发创造力，培养未来创新人才',
    heroTitle: '创新思维 · 实践能力',
    heroSubtitle: 'STEAM创客教育平台',
    heroDescription: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',
    contactEmail: '<EMAIL>',
    contactPhone: '************',
    contactAddress: '北京市朝阳区创新大厦',
    teamCount: 50,
    enableRegistration: true,
    statsProjects: 500,
    statsStudents: 1200,
    statsSatisfaction: 98
  }
}

export default async function Home() {
  const settings = await getSettings()

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-20 overflow-hidden">
        <div className="absolute top-10 left-10 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-float"></div>
        <div className="absolute bottom-10 right-10 w-24 h-24 bg-indigo-200 rounded-full opacity-20 animate-float-delayed"></div>
        <div className="absolute top-1/2 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-float-slow"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center animate-fade-in">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {settings.heroTitle}
              <span className="block text-blue-600">{settings.heroSubtitle}</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              {settings.heroDescription}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
              <Link href="/projects">
                <Button size="lg" className="w-full sm:w-auto btn-enhanced rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-enhanced">
                  探索作品展示
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
              <Link href="/apply">
                <Button variant="outline" size="lg" className="w-full sm:w-auto btn-enhanced rounded-xl border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white">
                  申请加入团队
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              为什么选择我们的创客教育
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              我们提供全方位的STEAM教育解决方案，让学习变得更有趣、更有效
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6 rounded-xl border border-gray-200 card-hover-enhanced bg-white shadow-enhanced">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Lightbulb className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">创新思维</h3>
              <p className="text-gray-600">
                培养学生的创新思维和问题解决能力，激发无限创造潜能
              </p>
            </div>

            <div className="text-center p-6 rounded-xl border border-gray-200 card-hover-enhanced bg-white shadow-enhanced">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">实践教学</h3>
              <p className="text-gray-600">
                通过动手实践，让学生在制作过程中掌握科学技术知识
              </p>
            </div>

            <div className="text-center p-6 rounded-xl border border-gray-200 card-hover-enhanced bg-white shadow-enhanced">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">团队协作</h3>
              <p className="text-gray-600">
                培养团队合作精神，提升沟通协调和项目管理能力
              </p>
            </div>

            <div className="text-center p-6 rounded-xl border border-gray-200 card-hover-enhanced bg-white shadow-enhanced">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">成果展示</h3>
              <p className="text-gray-600">
                展示学生优秀作品，建立自信心和成就感
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-gray-600">学生参与</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-green-600 mb-2">100+</div>
              <div className="text-gray-600">优秀作品</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-purple-600 mb-2">50+</div>
              <div className="text-gray-600">专业导师</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-orange-600 mb-2">20+</div>
              <div className="text-gray-600">合作学校</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-black opacity-10"></div>
        <div className="absolute top-10 right-10 w-40 h-40 bg-white rounded-full opacity-10 animate-float"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-white rounded-full opacity-10 animate-float-delayed"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 animate-fade-in">
            准备好开始你的创客之旅了吗？
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto animate-slide-up">
            加入我们的创客教育平台，与志同道合的伙伴一起探索科技的无限可能
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
            <Link href="/apply">
              <Button size="lg" className="w-full sm:w-auto btn-enhanced rounded-xl bg-white text-blue-600 hover:bg-gray-100 shadow-enhanced">
                立即申请加入
              </Button>
            </Link>
            <Link href="/about">
              <Button size="lg" variant="outline" className="w-full sm:w-auto btn-enhanced rounded-xl border-2 border-white text-white hover:bg-white hover:text-blue-600">
                了解更多
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface ProjectInteractionState {
  liked: boolean
  likeCount: number
  viewCount: number
  shareCount: number
  isLoading: boolean
  error: string | null
}

export function useProjectInteraction(projectId: string) {
  const { data: session } = useSession()
  const [state, setState] = useState<ProjectInteractionState>({
    liked: false,
    likeCount: 0,
    viewCount: 0,
    shareCount: 0,
    isLoading: true,
    error: null
  })

  // 获取初始状态
  useEffect(() => {
    if (!projectId) return

    const fetchInitialState = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: null }))

        // 获取点赞状态
        const likeResponse = await fetch(`/api/projects/${projectId}/like`)
        const likeData = await likeResponse.json()

        // 获取项目基本信息
        const projectResponse = await fetch(`/api/projects/${projectId}`)
        const projectData = await projectResponse.json()

        setState(prev => ({
          ...prev,
          liked: likeData.liked || false,
          likeCount: likeData.likeCount || 0,
          viewCount: projectData.viewCount || 0,
          shareCount: projectData.shareCount || 0,
          isLoading: false
        }))

        // 记录浏览
        await fetch(`/api/projects/${projectId}/view`, {
          method: 'POST'
        })
      } catch (error) {
        console.error('获取项目互动状态失败:', error)
        setState(prev => ({
          ...prev,
          error: '获取数据失败',
          isLoading: false
        }))
      }
    }

    fetchInitialState()
  }, [projectId])

  // 点赞/取消点赞
  const toggleLike = async () => {
    if (!session) {
      setState(prev => ({ ...prev, error: '请先登录' }))
      return
    }

    try {
      setState(prev => ({ ...prev, error: null }))

      const response = await fetch(`/api/projects/${projectId}/like`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('操作失败')
      }

      const data = await response.json()
      
      setState(prev => ({
        ...prev,
        liked: data.liked,
        likeCount: data.likeCount
      }))
    } catch (error) {
      console.error('点赞操作失败:', error)
      setState(prev => ({ ...prev, error: '操作失败，请稍后重试' }))
    }
  }

  // 分享
  const share = async (platform: string) => {
    try {
      setState(prev => ({ ...prev, error: null }))

      const response = await fetch(`/api/projects/${projectId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ platform })
      })

      if (!response.ok) {
        throw new Error('分享失败')
      }

      const data = await response.json()
      
      setState(prev => ({
        ...prev,
        shareCount: data.shareCount
      }))
    } catch (error) {
      console.error('分享失败:', error)
      setState(prev => ({ ...prev, error: '分享失败，请稍后重试' }))
    }
  }

  // 清除错误
  const clearError = () => {
    setState(prev => ({ ...prev, error: null }))
  }

  return {
    ...state,
    toggleLike,
    share,
    clearError,
    isLoggedIn: !!session
  }
}

'use client'

import { useState, useEffect } from 'react'

interface PublicSettings {
  // 基本信息
  site_name: string
  site_description: string
  site_slogan: string
  team_count: number
  copyright_year: string

  // 联系信息
  contact_phone: string
  contact_email: string
  contact_address: string
  contact_wechat: string
  contact_qq: string
  work_hours: string

  // 功能开关
  allow_registration: boolean
  show_team_section: boolean
  show_projects_section: boolean
  show_blog_section: boolean
  show_apply_section: boolean
  enable_comments: boolean
  enable_likes: boolean

  // 外观设置
  primary_color: string
  secondary_color: string
  hero_background: string
  show_animations: boolean
  dark_mode_enabled: boolean

  // 首页内容
  hero_title: string
  hero_subtitle: string
  hero_description: string
  about_intro: string

  // 统计数据
  stats_projects: number
  stats_students: number
  stats_satisfaction: number
  total_members: number
  active_members: number
  total_projects: number
  completed_projects: number

  // SEO设置
  meta_keywords: string
  meta_description: string

  // 法律文档
  terms_content: string
  privacy_content: string
  terms_last_updated: string
  privacy_last_updated: string

  // 自定义数据
  facilities_equipment: string
  classroom_specs: string
  main_equipment: string
}

const defaultSettings: PublicSettings = {
  // 基本信息
  site_name: '创客教育平台',
  site_description: '专业的STEAM教育平台',
  site_slogan: '创新思维 · 实践能力',
  team_count: 50,
  copyright_year: '2024',

  // 联系信息
  contact_phone: '************',
  contact_email: '<EMAIL>',
  contact_address: '北京市海淀区创新大厦',
  contact_wechat: 'makeredu2024',
  contact_qq: '123456789',
  work_hours: '周一至周五 9:00-18:00',

  // 功能开关
  allow_registration: true,
  show_team_section: true,
  show_projects_section: true,
  show_blog_section: true,
  show_apply_section: true,
  enable_comments: true,
  enable_likes: true,

  // 外观设置
  primary_color: '#3B82F6',
  secondary_color: '#6366F1',
  hero_background: 'gradient',
  show_animations: true,
  dark_mode_enabled: false,

  // 首页内容
  hero_title: '创新思维 · 实践能力',
  hero_subtitle: 'STEAM创客教育平台',
  hero_description: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。',
  about_intro: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。',

  // 统计数据
  stats_projects: 500,
  stats_students: 1000,
  stats_satisfaction: 98,
  total_members: 50,
  active_members: 35,
  total_projects: 120,
  completed_projects: 95,

  // SEO设置
  meta_keywords: 'STEAM教育,创客教育,3D打印,机器人,编程',
  meta_description: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程',

  // 法律文档
  terms_content: '',
  privacy_content: '',
  terms_last_updated: new Date().toISOString().split('T')[0],
  privacy_last_updated: new Date().toISOString().split('T')[0],

  // 自定义数据
  facilities_equipment: '[]',
  classroom_specs: '[]',
  main_equipment: '[]'
}

export function useSettings() {
  const [settings, setSettings] = useState<PublicSettings>(defaultSettings)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/settings/public')
      if (response.ok) {
        const data = await response.json()
        setSettings({ ...defaultSettings, ...data })
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error)
      // 使用默认设置
    } finally {
      setIsLoading(false)
    }
  }

  return { settings, isLoading, refetch: fetchSettings }
}

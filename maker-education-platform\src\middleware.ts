import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

// 检查维护模式的函数
async function checkMaintenanceMode() {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/settings/public`, {
      cache: 'no-store'
    })
    if (response.ok) {
      const settings = await response.json()
      return settings.maintenance_mode === true
    }
  } catch (error) {
    console.error('Failed to check maintenance mode:', error)
  }
  return false
}

export default withAuth(
  async function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // 检查维护模式
    const isMaintenanceMode = await checkMaintenanceMode()

    // 如果是维护模式，且不是管理员，且不是维护页面或API路径
    if (isMaintenanceMode &&
        !pathname.startsWith('/admin') &&
        !pathname.startsWith('/api') &&
        !pathname.startsWith('/auth') &&
        pathname !== '/maintenance' &&
        token?.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/maintenance', req.url))
    }

    // 如果访问维护页面但不是维护模式，重定向到首页
    if (pathname === '/maintenance' && !isMaintenanceMode) {
      return NextResponse.redirect(new URL('/', req.url))
    }

    // 检查是否访问管理员路径
    if (pathname.startsWith('/admin')) {
      // 如果没有登录，重定向到登录页面
      if (!token) {
        const loginUrl = new URL('/auth/signin', req.url)
        loginUrl.searchParams.set('callbackUrl', pathname)
        return NextResponse.redirect(loginUrl)
      }

      // 如果不是管理员，返回403
      if (token.role !== 'ADMIN') {
        return new NextResponse('Forbidden', { status: 403 })
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // 对于管理员路径，需要验证用户角色
        if (pathname.startsWith('/admin')) {
          return token?.role === 'ADMIN'
        }

        // 对于其他路径，允许访问
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|images|api/auth).*)',
  ]
}

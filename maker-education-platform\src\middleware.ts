import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // 检查是否访问管理员路径
    if (pathname.startsWith('/admin')) {
      // 如果没有登录，重定向到登录页面
      if (!token) {
        const loginUrl = new URL('/auth/signin', req.url)
        loginUrl.searchParams.set('callbackUrl', pathname)
        return NextResponse.redirect(loginUrl)
      }

      // 如果不是管理员，返回403
      if (token.role !== 'ADMIN') {
        return new NextResponse('Forbidden', { status: 403 })
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // 对于管理员路径，需要验证用户角色
        if (pathname.startsWith('/admin')) {
          return token?.role === 'ADMIN'
        }

        // 对于其他路径，允许访问
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    '/admin/:path*',
    // 可以添加其他需要保护的路径
  ]
}
